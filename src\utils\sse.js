/**
 * Server-Sent Events (SSE) 工具类
 * 用于处理流式AI回复的实时接收
 * 集成 SSE 频率分析功能
 */

// 动态导入分析器，避免循环依赖
let sseAnalyzer = null
try {
  import('./sseFrequencyAnalyzer.js').then(module => {
    sseAnalyzer = module.sseAnalyzer
  })
} catch (error) {
  console.warn('SSE频率分析器加载失败:', error)
}

/**
 * SSE连接管理器
 */
export class SSEManager {
  constructor() {
    this.connections = new Map() // 存储活跃的SSE连接
  }

  /**
   * 创建SSE连接并处理流式数据
   * @param {Object} options - 连接选项
   * @param {string} options.url - SSE接口URL
   * @param {Object} options.data - 请求数据
   * @param {Function} options.onMessage - 消息接收回调
   * @param {Function} options.onComplete - 完成回调
   * @param {Function} options.onError - 错误回调
   * @param {string} options.connectionId - 连接ID，用于管理连接
   * @returns {EventSource} EventSource实例
   */
  createConnection(options) {
    const {
      url,
      data,
      onMessage,
      onComplete,
      onError,
      connectionId = 'default'
    } = options

    // 关闭已存在的连接
    this.closeConnection(connectionId)

    try {
      // 构建URL参数
      const params = new URLSearchParams()
      Object.keys(data).forEach(key => {
        if (data[key] !== undefined && data[key] !== null) {
          params.append(key, data[key])
        }
      })

      const fullUrl = `${url}?${params.toString()}`
      console.log('🔗 创建SSE连接:', fullUrl)

      // 创建EventSource连接
      const eventSource = new EventSource(fullUrl)

      // 存储连接
      this.connections.set(connectionId, eventSource)

      // 监听消息事件
      eventSource.addEventListener('message', (event) => {
        try {
          const timestamp = performance.now()
          console.log('📨 收到SSE消息:', event.data)

          // 记录 SSE 事件用于频率分析
          if (sseAnalyzer) {
            sseAnalyzer.recordSSEEvent(event.data, timestamp)
          }

          if (onMessage) {
            onMessage(event.data)
          }
        } catch (error) {
          console.error('处理SSE消息失败:', error)
          if (onError) {
            onError(error)
          }
        }
      })

      // 监听完成事件
      eventSource.addEventListener('complete', (event) => {
        console.log('✅ SSE连接完成:', event.data)
        this.closeConnection(connectionId)
        if (onComplete) {
          onComplete(event.data)
        }
      })

      // 监听错误事件
      eventSource.addEventListener('error', (event) => {
        console.error('❌ SSE连接错误:', event)
        this.closeConnection(connectionId)
        if (onError) {
          onError(new Error('SSE连接错误'))
        }
      })

      // 监听自定义错误事件
      eventSource.addEventListener('error', (event) => {
        console.error('❌ SSE自定义错误:', event.data)
        this.closeConnection(connectionId)
        if (onError) {
          onError(new Error(event.data || 'SSE处理错误'))
        }
      })

      // 连接打开事件
      eventSource.onopen = () => {
        console.log('🚀 SSE连接已建立:', connectionId)
      }

      // 通用错误处理
      eventSource.onerror = (error) => {
        console.error('💥 SSE连接发生错误:', error)
        
        // 检查连接状态
        if (eventSource.readyState === EventSource.CLOSED) {
          console.log('🔒 SSE连接已关闭')
          this.closeConnection(connectionId)
        } else if (eventSource.readyState === EventSource.CONNECTING) {
          console.log('🔄 SSE正在重连...')
        }

        if (onError) {
          onError(error)
        }
      }

      return eventSource

    } catch (error) {
      console.error('创建SSE连接失败:', error)
      if (onError) {
        onError(error)
      }
      return null
    }
  }

  /**
   * 关闭指定的SSE连接
   * @param {string} connectionId - 连接ID
   */
  closeConnection(connectionId) {
    const connection = this.connections.get(connectionId)
    if (connection) {
      console.log('🔌 关闭SSE连接:', connectionId, '状态:', connection.readyState)
      connection.close()
      this.connections.delete(connectionId)
      console.log('✅ SSE连接已关闭并从管理器中移除:', connectionId)
    } else {
      console.log('⚠️ 未找到要关闭的SSE连接:', connectionId)
    }
  }

  /**
   * 关闭所有SSE连接
   */
  closeAllConnections() {
    console.log('🔌 关闭所有SSE连接')
    this.connections.forEach((connection, id) => {
      connection.close()
    })
    this.connections.clear()
  }

  /**
   * 获取活跃连接数量
   * @returns {number} 连接数量
   */
  getActiveConnectionCount() {
    return this.connections.size
  }

  /**
   * 检查指定连接是否活跃
   * @param {string} connectionId - 连接ID
   * @returns {boolean} 是否活跃
   */
  isConnectionActive(connectionId) {
    const connection = this.connections.get(connectionId)
    return connection && connection.readyState === EventSource.OPEN
  }
}

// 创建全局SSE管理器实例
export const sseManager = new SSEManager()

/**
 * 便捷的流式聊天函数
 * @param {Object} options - 聊天选项
 * @param {string} options.message - 用户消息
 * @param {string} options.chatId - 对话ID
 * @param {Function} options.onMessage - 消息接收回调
 * @param {Function} options.onComplete - 完成回调
 * @param {Function} options.onError - 错误回调
 * @returns {EventSource} EventSource实例
 */
export function streamChat(options) {
  const baseURL = process.env.VUE_APP_BASE_API || '/api'

  return sseManager.createConnection({
    url: `${baseURL}/ai/stream`, // 根据您的后端代码，这应该是正确的接口路径
    data: {
      message: options.message,
      chatId: options.chatId,
      type: 1
    },
    onMessage: options.onMessage,
    onComplete: options.onComplete,
    onError: options.onError,
    connectionId: options.chatId || 'default'
  })
}

/**
 * 停止指定对话的流式接收
 * @param {string} chatId - 对话ID
 */
export function stopStreamChat(chatId) {
  sseManager.closeConnection(chatId || 'default')
}

/**
 * 清理所有流式连接
 */
export function cleanupAllStreams() {
  sseManager.closeAllConnections()
}
