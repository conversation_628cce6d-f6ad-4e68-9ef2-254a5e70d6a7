import request from '@/utils/request'

/**
 * 获取对话列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.limit - 每页数量
 */
export function getConversations() {
  return request({
    url: '/ai/conversations',
    method: 'get',
  })
}

/**
 * 创建新对话
 * @param {Object} data - 对话数据
 * @param {string} data.title - 对话标题
 */
export function createConversation(data) {
  return request({
    url: '/ai/createConversation',
    method: 'post',
    data
  })
}

/**
 * 更新对话信息
 * @param {string} id - 对话ID
 * @param {Object} data - 更新数据
 * @param {string} data.title - 对话标题
 */
export function updateConversation(id, data) {
  return request({
    url: `/ai/updateConversation/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除对话
 * @param {string} id - 对话ID
 */
export function deleteConversation(id) {
  return request({
    url: `/chat/conversations/${id}`,
    method: 'delete'
  })
}

/**
 * 获取对话消息列表
 * @param {string} conversationId - 对话ID
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.limit - 每页数量
 */
export function getMessages(conversationId, params) {
  return request({
    url: `/ai/chatInfo/${conversationId}/messages`,
    method: 'get',
    params
  })
}

/**
 * 发送消息
 * @param {string} conversationId - 对话ID
 * @param {Object} data - 消息数据
 * @param {string} data.content - 消息内容
 * @param {string} data.type - 消息类型 (text, image, file)
 */
export function sendMessage(conversationId, data) {
  return request({
    url: `/chat/conversations/${conversationId}/messages`,
    method: 'post',
    data
  })
}

/**
 * 删除消息
 * @param {string} conversationId - 对话ID
 * @param {string} messageId - 消息ID
 */
export function deleteMessage(conversationId, messageId) {
  return request({
    url: `/chat/conversations/${conversationId}/messages/${messageId}`,
    method: 'delete'
  })
}

/**
 * AI对话接口
 * @param {Object} data - 对话数据
 * @param {string} data.message - 用户消息
 * @param {string} data.conversationId - 对话ID
 * @param {Array} data.history - 历史消息（可选）
 */
export function chatWithAI(data) {
  return request({
    url: '/ai/chat',
    method: 'post',
    data,
    timeout: 30000, // AI回复可能需要更长时间
    showLoading: false // 明确禁用全局加载状态
  })
}


/**
 * 多轮对话接口 - 返回文本格式回复
 * 用于发送单条消息给AI并获取文本回复
 * @param {Object} params - 请求参数
 * @param {string} params.message - 用户发送的消息内容
 * @returns {Promise} 返回AI的文本回复
 */
export function singleChatCompletionText(params) {
  // 提取跳过错误消息的标识
  const { _skipErrorMessage, ...apiParams } = params

  return request({
    url: '/ai/multiwheelGenerationResultJson',
    method: 'get',
    params: apiParams,
    // 超时时间由request拦截器自动设置为2分钟
    showLoading: false, // 明确禁用全局加载状态
    _skipErrorMessage: true, // 强制跳过错误消息
    // 添加自定义配置，完全禁用错误处理
    skipGlobalErrorHandler: true
  })
}

/**
 * 流式AI对话接口 - 使用SSE接收流式回复
 * @param {Object} options - 对话选项
 * @param {string} options.message - 用户消息
 * @param {string} options.chatId - 对话ID
 * @param {Function} options.onMessage - 消息接收回调
 * @param {Function} options.onComplete - 完成回调
 * @param {Function} options.onError - 错误回调
 * @returns {EventSource} EventSource实例
 */
export function streamChatWithAI(options) {
  // 动态导入SSE工具类，避免循环依赖
  return import('@/utils/sse').then(({ streamChat }) => {
    return streamChat(options)
  })
}



/**
 * 获取AI模型列表
 */
export function getAIModels() {
  return request({
    url: '/ai/models',
    method: 'get'
  })
}

/**
 * 导出对话记录
 * @param {string} conversationId - 对话ID
 * @param {string} format - 导出格式 (txt, pdf, json)
 */
export function exportConversation(conversationId, format = 'txt') {
  return request({
    url: `/chat/conversations/${conversationId}/export`,
    method: 'get',
    params: { format },
    responseType: 'blob' // 文件下载
  })
}

/**
 * 清空对话消息
 * @param {string} conversationId - 对话ID
 */
export function clearConversation(conversationId) {
  return request({
    url: `/ai/clearConversation/${conversationId}/clear`,
    method: 'delete'
  })
}
