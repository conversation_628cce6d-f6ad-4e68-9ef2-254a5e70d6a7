// SCSS 变量文件
// 这个文件会被自动导入到所有 Vue 组件中

// 颜色变量
$primary-color: #2563eb;
$primary-hover: #1d4ed8;
$success-color: #10b981;
$warning-color: #f59e0b;
$danger-color: #ef4444;

// 间距变量
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;

// 边框圆角
$border-radius-sm: 4px;
$border-radius-md: 6px;
$border-radius-lg: 8px;
$border-radius-xl: 12px;

// 阴影
$shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
$shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
$shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);

// 过渡动画
$transition-fast: 0.15s ease;
$transition-normal: 0.2s ease;
$transition-slow: 0.3s ease;



// 混入
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@mixin text-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

@mixin card-style {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: $border-radius-lg;
  box-shadow: $shadow-sm;
}
