<template>
  <div class="adaptive-markdown-renderer" ref="container">
    <!-- 使用双缓冲技术，确保渲染流畅 -->
    <div 
      ref="activeBuffer"
      class="markdown-buffer active-buffer"
      v-show="activeBufferIndex === 0"
    ></div>
    <div 
      ref="backBuffer"
      class="markdown-buffer back-buffer"
      v-show="activeBufferIndex === 1"
    ></div>
    
    <!-- 渲染状态指示器 -->
    <div v-if="showDebugInfo" class="debug-info">
      <span>SSE频率: {{ sseFrequency }}Hz</span>
      <span>渲染模式: {{ currentRenderMode }}</span>
      <span>缓冲区: {{ activeBufferIndex }}</span>
      <span>队列: {{ renderQueue.length }}</span>
    </div>
  </div>
</template>

<script>
// 导入 MarkdownIt
import MarkdownIt from 'markdown-it'

// 导入 SSE 频率分析器
import { sseAnalyzer } from '@/utils/sseFrequencyAnalyzer.js'

/**
 * 自适应 Markdown 渲染器
 * 根据 SSE 响应频率自动调整渲染策略
 */
export default {
  name: 'AdaptiveMarkdownRenderer',
  props: {
    content: {
      type: String,
      default: ''
    },
    isStreaming: {
      type: Boolean,
      default: false
    },
    showDebugInfo: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      md: null,
      renderCount: 0,
      lastContent: '',
      
      // 双缓冲相关
      activeBufferIndex: 0,
      
      // SSE 频率检测
      sseTimestamps: [],
      sseFrequency: 0,
      lastSSETime: 0,
      
      // 渲染策略
      currentRenderMode: 'auto',
      renderQueue: [],
      isRendering: false,
      
      // 定时器
      renderTimer: null,
      frequencyTimer: null,
      
      // 渲染策略配置
      renderModes: {
        // 高频模式：SSE > 20Hz，使用 requestAnimationFrame
        highFreq: {
          threshold: 20,
          method: 'raf',
          interval: 0
        },
        // 中频模式：SSE 5-20Hz，使用 16ms 定时器
        mediumFreq: {
          threshold: 5,
          method: 'timer',
          interval: 16
        },
        // 低频模式：SSE < 5Hz，立即渲染
        lowFreq: {
          threshold: 0,
          method: 'immediate',
          interval: 0
        }
      }
    }
  },
  created() {
    this.initMarkdown()
  },
  mounted() {
    // 立即渲染
    this.renderContent()
    
    // 监听全局事件
    if (this.$bus) {
      this.$bus.$on('force-markdown-update', this.handleForceUpdate)
      this.$bus.$on('force-markdown-update-delayed', this.handleForceUpdate)
    }
    
    // 开始频率检测
    this.startFrequencyDetection()
  },
  beforeDestroy() {
    this.cleanup()
  },
  watch: {
    content: {
      handler(newContent) {
        if (newContent !== this.lastContent) {
          this.recordSSEEvent()
          this.queueRender(newContent)
        }
      },
      immediate: true
    },
    
    isStreaming: {
      handler(newVal) {
        if (newVal) {
          this.startAdaptiveRendering()
        } else {
          this.stopAdaptiveRendering()
          // 最后一次渲染
          this.renderContent()
        }
      }
    }
  },
  methods: {
    // 初始化 Markdown - 与 test-ultimate-streaming.html 保持一致
    initMarkdown() {
      try {
        // 优先使用导入的 MarkdownIt
        if (MarkdownIt) {
          this.md = new MarkdownIt({
            html: true,
            xhtmlOut: false,
            breaks: true,
            linkify: true,
            typographer: true
          })
          console.log('✅ AdaptiveMarkdownRenderer: Markdown-it 初始化成功 (ES6 import)')
        } else if (window.markdownit) {
          // 备用方案：使用全局 markdownit
          this.md = window.markdownit({
            html: true,
            xhtmlOut: false,
            breaks: true,
            linkify: true,
            typographer: true
          })
          console.log('✅ AdaptiveMarkdownRenderer: Markdown-it 初始化成功 (window.markdownit)')
        } else {
          throw new Error('无法找到 MarkdownIt 库')
        }
      } catch (error) {
        console.error('❌ AdaptiveMarkdownRenderer: 初始化失败:', error)
        this.md = null
      }
    },
    
    // 记录 SSE 事件
    recordSSEEvent() {
      const now = performance.now()
      this.lastSSETime = now
      this.sseTimestamps.push(now)
      
      // 只保留最近 2 秒的数据
      const cutoff = now - 2000
      this.sseTimestamps = this.sseTimestamps.filter(t => t > cutoff)
    },
    
    // 开始频率检测
    startFrequencyDetection() {
      this.frequencyTimer = setInterval(() => {
        this.calculateSSEFrequency()
        this.adjustRenderStrategy()
      }, 500) // 每 500ms 检测一次
    },
    
    // 计算 SSE 频率
    calculateSSEFrequency() {
      if (this.sseTimestamps.length < 2) {
        this.sseFrequency = 0
        return
      }
      
      const now = performance.now()
      const recentEvents = this.sseTimestamps.filter(t => t > now - 1000) // 最近 1 秒
      this.sseFrequency = recentEvents.length
    },
    
    // 调整渲染策略
    adjustRenderStrategy() {
      let newMode = 'lowFreq'
      
      if (this.sseFrequency >= this.renderModes.highFreq.threshold) {
        newMode = 'highFreq'
      } else if (this.sseFrequency >= this.renderModes.mediumFreq.threshold) {
        newMode = 'mediumFreq'
      }
      
      if (newMode !== this.currentRenderMode) {
        console.log(`🔄 切换渲染模式: ${this.currentRenderMode} → ${newMode} (SSE: ${this.sseFrequency}Hz)`)
        this.currentRenderMode = newMode
        this.restartAdaptiveRendering()
      }
    },
    
    // 开始自适应渲染
    startAdaptiveRendering() {
      this.stopAdaptiveRendering()
      
      const mode = this.renderModes[this.currentRenderMode]
      
      if (mode.method === 'raf') {
        // 使用 requestAnimationFrame
        const renderLoop = () => {
          if (this.isStreaming) {
            this.processRenderQueue()
            this.renderTimer = requestAnimationFrame(renderLoop)
          }
        }
        this.renderTimer = requestAnimationFrame(renderLoop)
        
      } else if (mode.method === 'timer') {
        // 使用定时器
        this.renderTimer = setInterval(() => {
          if (this.isStreaming) {
            this.processRenderQueue()
          }
        }, mode.interval)
        
      } else if (mode.method === 'immediate') {
        // 立即渲染模式，不需要定时器
        // 渲染会在 queueRender 中立即执行
      }
    },
    
    // 停止自适应渲染
    stopAdaptiveRendering() {
      if (this.renderTimer) {
        if (this.renderModes[this.currentRenderMode]?.method === 'raf') {
          cancelAnimationFrame(this.renderTimer)
        } else {
          clearInterval(this.renderTimer)
        }
        this.renderTimer = null
      }
    },
    
    // 重启自适应渲染
    restartAdaptiveRendering() {
      if (this.isStreaming) {
        this.startAdaptiveRendering()
      }
    },
    
    // 队列渲染
    queueRender(content) {
      this.renderQueue.push({
        content,
        timestamp: performance.now()
      })
      
      // 立即渲染模式
      if (this.currentRenderMode === 'lowFreq') {
        this.processRenderQueue()
      }
    },
    
    // 处理渲染队列
    processRenderQueue() {
      if (this.isRendering || this.renderQueue.length === 0) {
        return
      }
      
      // 取最新的渲染请求
      const latestRender = this.renderQueue.pop()
      this.renderQueue = [] // 清空队列
      
      this.renderContent(latestRender.content)
    },
    
    // 核心渲染方法（使用双缓冲）
    renderContent(content = this.content) {
      if (this.isRendering) return

      this.isRendering = true

      try {
        const startTime = performance.now()

        // 获取后台缓冲区
        const backBufferIndex = 1 - this.activeBufferIndex
        const backBuffer = backBufferIndex === 0 ? this.$refs.activeBuffer : this.$refs.backBuffer

        if (!backBuffer) {
          this.isRendering = false
          return
        }

        let rendered = ''

        if (this.md && content) {
          rendered = this.md.render(content)
        } else if (content) {
          rendered = this.simpleMarkdownParse(content)
        }

        // 在后台缓冲区渲染
        backBuffer.innerHTML = rendered

        // 切换缓冲区
        this.activeBufferIndex = backBufferIndex

        // 更新状态
        this.lastContent = content
        this.renderCount++

        const renderTime = performance.now() - startTime

        // 记录渲染事件用于频率分析
        if (sseAnalyzer && this.isStreaming) {
          sseAnalyzer.recordRenderEvent(renderTime)
        }

        if (this.isStreaming) {
          console.log(`🎨 Adaptive渲染: ${content?.length || 0} 字符, 耗时 ${renderTime.toFixed(2)}ms, 模式: ${this.currentRenderMode}, 第${this.renderCount}次`)
        }

        this.$emit('render-count', this.renderCount)

      } catch (error) {
        console.error('❌ Adaptive渲染失败:', error)
      } finally {
        this.isRendering = false
      }
    },
    
    // 简单的 Markdown 解析（备用方案）
    simpleMarkdownParse(text) {
      if (!text) return ''
      
      return text
        .replace(/^### (.*$)/gim, '<h3>$1</h3>')
        .replace(/^## (.*$)/gim, '<h2>$1</h2>')
        .replace(/^# (.*$)/gim, '<h1>$1</h1>')
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
        .replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>')
        .replace(/`(.*?)`/g, '<code>$1</code>')
        .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank">$1</a>')
        .replace(/^\* (.*$)/gim, '<li>$1</li>')
        .replace(/^- (.*$)/gim, '<li>$1</li>')
        .replace(/^\d+\. (.*$)/gim, '<li>$1</li>')
        .replace(/^> (.*$)/gim, '<blockquote>$1</blockquote>')
        .replace(/\n\n/g, '</p><p>')
        .replace(/\n/g, '<br>')
        .replace(/^(.*)$/, '<p>$1</p>')
    },
    
    // 处理强制更新事件
    handleForceUpdate(eventData) {
      if (this.isStreaming && eventData) {
        this.recordSSEEvent()
        this.queueRender(this.content)
      }
    },
    
    // 清理资源
    cleanup() {
      this.stopAdaptiveRendering()
      
      if (this.frequencyTimer) {
        clearInterval(this.frequencyTimer)
        this.frequencyTimer = null
      }
      
      if (this.$bus) {
        this.$bus.$off('force-markdown-update', this.handleForceUpdate)
        this.$bus.$off('force-markdown-update-delayed', this.handleForceUpdate)
      }
    }
  }
}
</script>

<style scoped>
.adaptive-markdown-renderer {
  line-height: 1.6;
  color: var(--text-primary, #333);
  word-wrap: break-word;
  font-size: 14px;
  position: relative;
}

.markdown-buffer {
  min-height: 20px;
}

.debug-info {
  position: absolute;
  top: -25px;
  right: 0;
  font-size: 10px;
  color: #999;
  display: flex;
  gap: 10px;
  background: rgba(255, 255, 255, 0.9);
  padding: 2px 8px;
  border-radius: 4px;
  border: 1px solid #eee;
}

/* 继承 Markdown 样式 */
.adaptive-markdown-renderer >>> h1,
.adaptive-markdown-renderer >>> h2,
.adaptive-markdown-renderer >>> h3,
.adaptive-markdown-renderer >>> h4,
.adaptive-markdown-renderer >>> h5,
.adaptive-markdown-renderer >>> h6 {
  color: #333;
  margin: 16px 0 12px 0;
  font-weight: 600;
  line-height: 1.4;
}

.adaptive-markdown-renderer >>> h1 {
  font-size: 1.8em;
  border-bottom: 2px solid #eee;
  padding-bottom: 8px;
}

.adaptive-markdown-renderer >>> h2 {
  font-size: 1.5em;
  border-bottom: 1px solid #eee;
  padding-bottom: 6px;
}

.adaptive-markdown-renderer >>> h3 {
  font-size: 1.3em;
  color: #409eff;
}

.adaptive-markdown-renderer >>> p {
  margin: 12px 0;
  line-height: 1.7;
}

.adaptive-markdown-renderer >>> strong {
  font-weight: 700;
  color: #409eff;
}

.adaptive-markdown-renderer >>> em {
  font-style: italic;
  color: #666;
}

.adaptive-markdown-renderer >>> code {
  background: rgba(64, 158, 255, 0.1);
  color: #409eff;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Courier New', Monaco, monospace;
  font-size: 0.9em;
}

.adaptive-markdown-renderer >>> pre {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
  margin: 16px 0;
  overflow-x: auto;
  font-family: 'Courier New', Monaco, monospace;
  font-size: 0.9em;
  line-height: 1.4;
}

.adaptive-markdown-renderer >>> blockquote {
  margin: 16px 0;
  padding: 12px 16px;
  border-left: 4px solid #409eff;
  background: rgba(64, 158, 255, 0.05);
  border-radius: 0 8px 8px 0;
  font-style: italic;
  color: #666;
}

.adaptive-markdown-renderer >>> ul,
.adaptive-markdown-renderer >>> ol {
  margin: 12px 0;
  padding-left: 20px;
}

.adaptive-markdown-renderer >>> li {
  margin: 6px 0;
  line-height: 1.6;
}

.adaptive-markdown-renderer >>> table {
  width: 100%;
  border-collapse: collapse;
  margin: 16px 0;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
}

.adaptive-markdown-renderer >>> th,
.adaptive-markdown-renderer >>> td {
  padding: 12px 16px;
  border-bottom: 1px solid #e9ecef;
  text-align: left;
}

.adaptive-markdown-renderer >>> th {
  background: rgba(64, 158, 255, 0.1);
  font-weight: 600;
  color: #409eff;
}

.adaptive-markdown-renderer >>> a {
  color: #409eff;
  text-decoration: none;
}

.adaptive-markdown-renderer >>> a:hover {
  text-decoration: underline;
}
</style>
