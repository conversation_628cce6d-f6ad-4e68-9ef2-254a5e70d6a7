/**
 * VIP功能测试工具
 * 用于测试VIP相关功能的完整流程
 */

// 模拟VIP数据
export const mockVipData = {
  free: {
    vipLevel: 'free',
    vipExpireTime: null,
    balance: 0,
    totalSpent: 0,
    dailyUsage: {
      date: new Date().toDateString(),
      messageCount: 15, // 已使用15条消息
      modelUsage: { basic: 15 }
    }
  },
  vip: {
    vipLevel: 'vip',
    vipExpireTime: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30天后到期
    balance: 5000, // 50元余额
    totalSpent: 2900, // 已消费29元
    dailyUsage: {
      date: new Date().toDateString(),
      messageCount: 50,
      modelUsage: { basic: 30, advanced: 20 }
    }
  },
  superVip: {
    vipLevel: 'super_vip',
    vipExpireTime: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(), // 1年后到期
    balance: 15000, // 150元余额
    totalSpent: 9900, // 已消费99元
    dailyUsage: {
      date: new Date().toDateString(),
      messageCount: 100,
      modelUsage: { basic: 30, advanced: 40, premium: 30 }
    }
  }
}

/**
 * 测试VIP权限检查
 * @param {Object} store - Vuex store实例
 * @param {string} vipLevel - VIP等级
 */
export function testVipPermissions(store, vipLevel = 'free') {
  console.log('🧪 开始测试VIP权限检查...')
  
  // 设置测试数据
  const testData = mockVipData[vipLevel]
  store.commit('user/SET_VIP_INFO', testData)
  store.commit('user/UPDATE_DAILY_USAGE', testData.dailyUsage)
  
  // 测试权限检查
  const canSendMessage = store.dispatch('user/checkUserPermission', {
    action: 'sendMessage'
  })
  
  const canUseAdvancedModel = store.dispatch('user/checkUserPermission', {
    action: 'useModel',
    model: 'advanced'
  })
  
  console.log('📊 权限测试结果:', {
    vipLevel,
    canSendMessage,
    canUseAdvancedModel,
    remainingMessages: store.getters['user/remainingMessages'],
    currentPlan: store.getters['user/currentVipPlan'].name
  })
  
  return {
    canSendMessage,
    canUseAdvancedModel
  }
}

/**
 * 测试VIP购买流程
 * @param {Object} store - Vuex store实例
 */
export async function testVipPurchase(store) {
  console.log('🧪 开始测试VIP购买流程...')
  
  try {
    const result = await store.dispatch('user/purchaseVip', {
      planId: 'vip',
      duration: 30,
      paymentMethod: 'alipay'
    })
    
    console.log('💳 VIP购买测试结果:', result)
    return result
  } catch (error) {
    console.error('❌ VIP购买测试失败:', error)
    return { success: false, error: error.message }
  }
}

/**
 * 测试充值流程
 * @param {Object} store - Vuex store实例
 */
export async function testRecharge(store) {
  console.log('🧪 开始测试充值流程...')
  
  try {
    const result = await store.dispatch('user/recharge', {
      amount: 10000, // 100元
      paymentMethod: 'alipay'
    })
    
    console.log('💰 充值测试结果:', result)
    console.log('💰 充值后余额:', store.getters['user/balanceYuan'])
    return result
  } catch (error) {
    console.error('❌ 充值测试失败:', error)
    return { success: false, error: error.message }
  }
}

/**
 * 运行完整的VIP功能测试套件
 * @param {Object} store - Vuex store实例
 */
export async function runVipTestSuite(store) {
  console.log('🚀 开始运行VIP功能完整测试套件...')
  
  const results = {
    permissions: {},
    purchase: null,
    recharge: null
  }
  
  // 测试不同VIP等级的权限
  results.permissions.free = testVipPermissions(store, 'free')
  results.permissions.vip = testVipPermissions(store, 'vip')
  results.permissions.superVip = testVipPermissions(store, 'superVip')
  
  // 测试购买流程
  results.purchase = await testVipPurchase(store)
  
  // 测试充值流程
  results.recharge = await testRecharge(store)
  
  console.log('✅ VIP功能测试套件完成:', results)
  return results
}

/**
 * 重置VIP测试数据
 * @param {Object} store - Vuex store实例
 */
export function resetVipTestData(store) {
  console.log('🔄 重置VIP测试数据...')
  
  store.commit('user/SET_VIP_INFO', {
    vipLevel: 'free',
    vipExpireTime: null,
    balance: 0,
    totalSpent: 0
  })
  
  store.commit('user/UPDATE_DAILY_USAGE', {
    messageCount: 0,
    modelUsage: {}
  })
  
  console.log('✅ VIP测试数据已重置')
}

// 导出测试工具
export default {
  mockVipData,
  testVipPermissions,
  testVipPurchase,
  testRecharge,
  runVipTestSuite,
  resetVipTestData
}
