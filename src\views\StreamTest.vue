<template>
  <div class="stream-test">
    <h2>流式输出测试页面</h2>
    
    <div class="test-controls">
      <el-input
        v-model="testMessage"
        placeholder="输入测试消息"
        style="width: 300px; margin-right: 10px;"
      />
      <el-button 
        type="primary" 
        @click="startStreamTest"
        :disabled="isStreaming"
      >
        {{ isStreaming ? '流式输出中...' : '开始流式测试' }}
      </el-button>
      <el-button 
        type="danger" 
        @click="stopStreamTest"
        :disabled="!isStreaming"
      >
        停止流式输出
      </el-button>
    </div>

    <div class="test-output">
      <h3>流式输出结果：</h3>
      <div class="output-content" :class="{ 'streaming': isStreaming }">
        {{ streamContent }}
        <span v-if="isStreaming" class="cursor">|</span>
      </div>
      
      <div class="test-info">
        <p><strong>状态：</strong>{{ streamStatus }}</p>
        <p><strong>已接收字符数：</strong>{{ streamContent.length }}</p>
        <p><strong>连接状态：</strong>{{ connectionStatus }}</p>
      </div>
    </div>

    <div class="test-logs">
      <h3>测试日志：</h3>
      <div class="logs-content">
        <div 
          v-for="(log, index) in testLogs" 
          :key="index"
          :class="['log-item', log.type]"
        >
          <span class="log-time">{{ log.time }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'StreamTest',
  data() {
    return {
      testMessage: '请介绍一下Vue.js的特点',
      streamContent: '',
      isStreaming: false,
      streamStatus: '未开始',
      connectionStatus: '未连接',
      testLogs: [],
      currentEventSource: null
    }
  },
  methods: {
    async startStreamTest() {
      if (!this.testMessage.trim()) {
        this.$message.warning('请输入测试消息')
        return
      }

      this.resetTest()
      this.isStreaming = true
      this.streamStatus = '连接中...'
      this.addLog('info', '开始流式测试')

      try {
        // 动态导入SSE工具类
        const { streamChat } = await import('@/utils/sse')
        
        this.addLog('info', `发送消息: ${this.testMessage}`)
        
        // 开始流式对话
        this.currentEventSource = streamChat({
          message: this.testMessage,
          chatId: 'test-' + Date.now(),
          
          onMessage: (data) => {
            this.addLog('success', `收到数据: ${data}`)
            this.streamContent += data
            this.streamStatus = '接收中...'
            this.connectionStatus = '已连接'
          },
          
          onComplete: (data) => {
            this.addLog('success', `流式完成: ${data}`)
            this.isStreaming = false
            this.streamStatus = '已完成'
            this.connectionStatus = '已断开'
          },
          
          onError: (error) => {
            this.addLog('error', `流式错误: ${error.message}`)
            this.isStreaming = false
            this.streamStatus = '错误'
            this.connectionStatus = '连接错误'
            this.$message.error('流式输出测试失败: ' + error.message)
          }
        })

        this.addLog('info', 'SSE连接已建立')
        this.connectionStatus = '连接中...'

      } catch (error) {
        this.addLog('error', `测试失败: ${error.message}`)
        this.isStreaming = false
        this.streamStatus = '失败'
        this.connectionStatus = '连接失败'
        this.$message.error('启动流式测试失败: ' + error.message)
      }
    },

    stopStreamTest() {
      if (this.currentEventSource) {
        this.currentEventSource.close()
        this.currentEventSource = null
        this.addLog('warning', '手动停止流式输出')
      }
      
      this.isStreaming = false
      this.streamStatus = '已停止'
      this.connectionStatus = '已断开'
    },

    resetTest() {
      this.streamContent = ''
      this.testLogs = []
      this.streamStatus = '未开始'
      this.connectionStatus = '未连接'
      
      if (this.currentEventSource) {
        this.currentEventSource.close()
        this.currentEventSource = null
      }
    },

    addLog(type, message) {
      this.testLogs.push({
        type,
        message,
        time: new Date().toLocaleTimeString()
      })
      
      // 限制日志数量
      if (this.testLogs.length > 50) {
        this.testLogs.shift()
      }
    }
  },

  beforeDestroy() {
    // 清理连接
    if (this.currentEventSource) {
      this.currentEventSource.close()
    }
  }
}
</script>

<style lang="scss" scoped>
.stream-test {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.test-controls {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}

.test-output {
  margin-bottom: 20px;
  
  .output-content {
    background: #f5f5f5;
    padding: 15px;
    border-radius: 8px;
    min-height: 100px;
    font-family: monospace;
    white-space: pre-wrap;
    position: relative;
    
    &.streaming {
      border-left: 3px solid #409eff;
    }
    
    .cursor {
      animation: blink 1s infinite;
      color: #409eff;
      font-weight: bold;
    }
  }
  
  .test-info {
    margin-top: 10px;
    
    p {
      margin: 5px 0;
      font-size: 14px;
    }
  }
}

.test-logs {
  .logs-content {
    background: #2d3748;
    color: #e2e8f0;
    padding: 15px;
    border-radius: 8px;
    max-height: 300px;
    overflow-y: auto;
    font-family: monospace;
    font-size: 12px;
  }
  
  .log-item {
    margin-bottom: 5px;
    
    &.info { color: #63b3ed; }
    &.success { color: #68d391; }
    &.warning { color: #fbd38d; }
    &.error { color: #fc8181; }
    
    .log-time {
      color: #a0aec0;
      margin-right: 10px;
    }
  }
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}
</style>
