<template>
  <div class="worker-markdown-renderer" ref="container">
    <!-- 直接渲染内容，无任何延迟 -->
    <div 
      v-html="renderedContent" 
      class="markdown-content"
      :key="updateKey"
    ></div>
    
    <!-- 渲染状态指示器 -->
    <div v-if="isRendering && isStreaming" class="render-indicator">
      <span class="render-dot"></span>
      渲染中...
    </div>
  </div>
</template>

<script>
/**
 * 基于 Web Worker 的超高速 Markdown 渲染器
 * 使用后台线程进行渲染，完全不阻塞主线程
 */
export default {
  name: 'WorkerMarkdownRenderer',
  props: {
    content: {
      type: String,
      default: ''
    },
    isStreaming: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      renderedContent: '',
      updateKey: 0,
      renderCount: 0,
      isRendering: false,
      worker: null,
      pendingRenders: new Map(),
      lastContent: '',
      renderQueue: [],
      isProcessingQueue: false
    }
  },
  created() {
    this.initWorker()
  },
  mounted() {
    // 立即渲染
    this.queueRender(this.content)
    
    // 监听全局事件
    if (this.$bus) {
      this.$bus.$on('force-markdown-update', this.handleForceUpdate)
      this.$bus.$on('force-markdown-update-delayed', this.handleForceUpdate)
    }
  },
  beforeDestroy() {
    this.cleanup()
  },
  watch: {
    content: {
      handler(newContent) {
        if (newContent !== this.lastContent) {
          this.queueRender(newContent)
        }
      },
      immediate: true
    }
  },
  methods: {
    // 初始化 Web Worker
    initWorker() {
      try {
        // 创建 Worker
        this.worker = new Worker('/src/workers/markdownWorker.js')
        
        // 监听 Worker 消息
        this.worker.onmessage = (e) => {
          this.handleWorkerMessage(e.data)
        }
        
        // 监听 Worker 错误
        this.worker.onerror = (error) => {
          console.error('❌ Worker 错误:', error)
          this.fallbackRender()
        }
        
        console.log('✅ WorkerMarkdownRenderer: Worker 初始化成功')
      } catch (error) {
        console.error('❌ WorkerMarkdownRenderer: Worker 初始化失败:', error)
        this.fallbackRender()
      }
    },
    
    // 队列渲染请求
    queueRender(content) {
      // 添加到队列
      this.renderQueue.push({
        content,
        timestamp: Date.now(),
        id: `render_${Date.now()}_${Math.random()}`
      })
      
      // 处理队列
      this.processRenderQueue()
    },
    
    // 处理渲染队列
    async processRenderQueue() {
      if (this.isProcessingQueue || this.renderQueue.length === 0) {
        return
      }
      
      this.isProcessingQueue = true
      
      while (this.renderQueue.length > 0) {
        // 取最新的渲染请求（丢弃过时的请求）
        const renderRequest = this.renderQueue.pop()
        this.renderQueue = [] // 清空队列，只处理最新的
        
        await this.performRender(renderRequest)
        
        // 如果是流式状态，稍微延迟以避免过度渲染
        if (this.isStreaming && this.renderQueue.length === 0) {
          await new Promise(resolve => setTimeout(resolve, 16)) // ~60fps
        }
      }
      
      this.isProcessingQueue = false
    },
    
    // 执行渲染
    async performRender(renderRequest) {
      const { content, id } = renderRequest
      
      if (!this.worker) {
        this.fallbackRender(content)
        return
      }
      
      try {
        this.isRendering = true
        
        // 记录待处理的渲染
        this.pendingRenders.set(id, {
          content,
          startTime: performance.now()
        })
        
        // 发送到 Worker
        this.worker.postMessage({
          id,
          content,
          options: {}
        })
        
      } catch (error) {
        console.error('❌ 发送到 Worker 失败:', error)
        this.fallbackRender(content)
      }
    },
    
    // 处理 Worker 返回的消息
    handleWorkerMessage(data) {
      const { id, success, rendered, renderTime, error, method } = data
      
      if (!this.pendingRenders.has(id)) {
        return // 过时的渲染结果
      }
      
      const renderInfo = this.pendingRenders.get(id)
      this.pendingRenders.delete(id)
      
      if (success) {
        // 更新渲染内容
        this.renderedContent = rendered
        this.lastContent = renderInfo.content
        this.renderCount++
        this.updateKey++
        this.isRendering = false
        
        const totalTime = performance.now() - renderInfo.startTime
        
        if (this.isStreaming) {
          console.log(`🚀 Worker渲染: ${renderInfo.content?.length || 0} 字符, Worker耗时 ${renderTime?.toFixed(2) || 'N/A'}ms, 总耗时 ${totalTime.toFixed(2)}ms, 方法: ${method}, 第${this.renderCount}次`)
        }
        
        // 发射事件
        this.$emit('render-count', this.renderCount)
        
        // 强制更新
        this.$nextTick(() => {
          this.$forceUpdate()
        })
        
      } else {
        console.error('❌ Worker 渲染失败:', error)
        this.fallbackRender(renderInfo.content)
      }
    },
    
    // 备用渲染方法
    fallbackRender(content = this.content) {
      try {
        // 简单的文本到 HTML 转换
        this.renderedContent = content ? content.replace(/\n/g, '<br>') : ''
        this.lastContent = content
        this.renderCount++
        this.updateKey++
        this.isRendering = false
        
        console.log('⚠️ 使用备用渲染方法')
        
        this.$emit('render-count', this.renderCount)
        
      } catch (error) {
        console.error('❌ 备用渲染也失败:', error)
        this.renderedContent = content || ''
      }
    },
    
    // 处理强制更新事件
    handleForceUpdate(eventData) {
      if (this.isStreaming && eventData) {
        this.queueRender(this.content)
      }
    },
    
    // 清理资源
    cleanup() {
      if (this.worker) {
        this.worker.terminate()
        this.worker = null
      }
      
      this.pendingRenders.clear()
      this.renderQueue = []
      
      if (this.$bus) {
        this.$bus.$off('force-markdown-update', this.handleForceUpdate)
        this.$bus.$off('force-markdown-update-delayed', this.handleForceUpdate)
      }
    }
  }
}
</script>

<style scoped>
.worker-markdown-renderer {
  line-height: 1.6;
  color: var(--text-primary, #333);
  word-wrap: break-word;
  font-size: 14px;
  position: relative;
}

.render-indicator {
  position: absolute;
  top: -20px;
  right: 0;
  font-size: 12px;
  color: #999;
  display: flex;
  align-items: center;
  gap: 4px;
}

.render-dot {
  width: 6px;
  height: 6px;
  background: #409eff;
  border-radius: 50%;
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.3; }
}

/* 继承之前的 Markdown 样式 */
.worker-markdown-renderer >>> h1,
.worker-markdown-renderer >>> h2,
.worker-markdown-renderer >>> h3,
.worker-markdown-renderer >>> h4,
.worker-markdown-renderer >>> h5,
.worker-markdown-renderer >>> h6 {
  color: #333;
  margin: 16px 0 12px 0;
  font-weight: 600;
  line-height: 1.4;
}

.worker-markdown-renderer >>> h1 {
  font-size: 1.8em;
  border-bottom: 2px solid #eee;
  padding-bottom: 8px;
}

.worker-markdown-renderer >>> h2 {
  font-size: 1.5em;
  border-bottom: 1px solid #eee;
  padding-bottom: 6px;
}

.worker-markdown-renderer >>> h3 {
  font-size: 1.3em;
  color: #409eff;
}

.worker-markdown-renderer >>> p {
  margin: 12px 0;
  line-height: 1.7;
}

.worker-markdown-renderer >>> strong {
  font-weight: 700;
  color: #409eff;
}

.worker-markdown-renderer >>> em {
  font-style: italic;
  color: #666;
}

.worker-markdown-renderer >>> code {
  background: rgba(64, 158, 255, 0.1);
  color: #409eff;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Courier New', Monaco, monospace;
  font-size: 0.9em;
}

.worker-markdown-renderer >>> pre {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
  margin: 16px 0;
  overflow-x: auto;
  font-family: 'Courier New', Monaco, monospace;
  font-size: 0.9em;
  line-height: 1.4;
}

.worker-markdown-renderer >>> blockquote {
  margin: 16px 0;
  padding: 12px 16px;
  border-left: 4px solid #409eff;
  background: rgba(64, 158, 255, 0.05);
  border-radius: 0 8px 8px 0;
  font-style: italic;
  color: #666;
}
</style>
