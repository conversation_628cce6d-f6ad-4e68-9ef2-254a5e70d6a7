<template>
  <div class="markdown-test">
    <div class="test-header">
      <h1>Markdown 渲染器测试 (markdown-it)</h1>
      <div class="controls">
        <el-switch v-model="enableHighlight" active-text="代码高亮"></el-switch>
        <el-switch v-model="enableAnchor" active-text="锚点"></el-switch>
        <el-switch v-model="enableTaskLists" active-text="任务列表"></el-switch>
        <el-switch v-model="enableToc" active-text="目录"></el-switch>
      </div>
    </div>

    <div class="test-container">
      <div class="input-section">
        <h3>输入 Markdown 内容：</h3>
        <el-input
          type="textarea"
          v-model="testMarkdown"
          :rows="25"
          placeholder="在这里输入 Markdown 内容进行测试..."
        />
      </div>

      <div class="output-section">
        <h3>渲染结果：</h3>
        <div class="rendered-output">
          <MarkdownRenderer
            :content="testMarkdown"
            :enable-html="true"
            :enable-breaks="true"
            :enable-linkify="true"
            :enable-typographer="true"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import MarkdownRenderer from '@/components/MarkdownRendererOptimized.vue'

export default {
  name: 'MarkdownTest',
  components: {
    MarkdownRenderer
  },
  data() {
    return {
      enableHighlight: true,
      enableAnchor: true,
      enableTaskLists: true,
      enableToc: true,
      testMarkdown: `# 人工智能（Artificial Intelligence，简称AI）

[[toc]]

人工智能（Artificial Intelligence，简称AI）是指由人创造的能够感知环境、学习知识、逻辑推理，并执行任务的智能体。它是一门研究如何让计算机模拟人类智能行为的科学技术，旨在使机器具备处理复杂问题的能力。

## 从广义和狭义两个角度来理解人工智能：

### ✅ 一、广义的人工智能（AI）

指的是任何能够执行通常需要人类智能才能完成任务的系统或机器。这些任务包括但不限于：

- 学习（Learning）
- 推理（Reasoning）
- 问题求解（Problem Solving）
- 知识表示（Knowledge Representation）
- 规划（Planning）
- 自然语言理解（Natural Language Understanding）
- 感知（Perception）
- 运动控制（Motion Control）
- 决策（Decision Making）

### ✅ 二、狭义的人工智能（Narrow AI）

也叫"弱人工智能"，指的是在特定任务或领域中表现出类人智能水平的系统，但它并不具备真正的人类意识或通用智能。例如：

- 语音助手（如 Siri、Alexa）
- 图像识别系统（如人脸识别）
- 推荐系统（如抖音、淘宝推荐）
- 自动驾驶汽车

目前我们接触到的绝大多数 AI 基本上都属于**狭义人工智能**。

## 代码示例

### JavaScript 示例
\`\`\`javascript
// AI 聊天机器人示例
class ChatBot {
  constructor(name) {
    this.name = name;
    this.responses = {
      'hello': 'Hi there! How can I help you?',
      'bye': 'Goodbye! Have a great day!',
      'default': 'I\\'m sorry, I don\\'t understand.'
    };
  }

  respond(input) {
    const key = input.toLowerCase();
    return this.responses[key] || this.responses['default'];
  }
}

const bot = new ChatBot('AI Assistant');
console.log(bot.respond('hello')); // Hi there! How can I help you?
\`\`\`

### Python 示例
\`\`\`python
import numpy as np
from sklearn.linear_model import LinearRegression

# 简单的机器学习示例
def train_model(X, y):
    """训练线性回归模型"""
    model = LinearRegression()
    model.fit(X, y)
    return model

# 示例数据
X = np.array([[1], [2], [3], [4], [5]])
y = np.array([2, 4, 6, 8, 10])

# 训练模型
model = train_model(X, y)
print(f"预测结果: {model.predict([[6]])}")  # 预测结果: [12.]
\`\`\`

### TypeScript 示例
\`\`\`typescript
interface AIModel {
  name: string;
  version: string;
  predict(input: any[]): Promise<any>;
}

class NeuralNetwork implements AIModel {
  name: string;
  version: string;

  constructor(name: string, version: string) {
    this.name = name;
    this.version = version;
  }

  async predict(input: number[]): Promise<number[]> {
    // 模拟神经网络预测
    return input.map(x => x * 0.8 + 0.2);
  }
}

const model = new NeuralNetwork('SimpleNN', '1.0.0');
model.predict([1, 2, 3]).then(result => {
  console.log('预测结果:', result);
});
\`\`\`

## 任务列表

- [x] 完成 AI 基础概念介绍
- [x] 添加代码示例
- [ ] 深入学习机器学习算法
- [ ] 研究深度学习框架
- [/] 实践自然语言处理项目
- [-] 已取消的任务

## 表格示例

| AI 类型 | 特点 | 应用场景 | 发展状态 |
|---------|------|----------|----------|
| 弱人工智能 | 专门化、任务特定 | 语音识别、图像分类 | 已商用 |
| 强人工智能 | 通用化、类人智能 | 通用问题解决 | 研发中 |
| 超人工智能 | 超越人类智能 | 科学研究、创新 | 理论阶段 |

## 引用

> "人工智能将是人类历史上最重要的技术发展之一。它将改变我们工作、生活和思考的方式。"
>
> —— 某位 AI 专家

> 嵌套引用示例：
> > "机器学习是实现人工智能的一种方法，但不是唯一方法。"
> >
> > —— 机器学习专家

## 链接和图片

- [OpenAI 官网](https://openai.com)
- [Google AI](https://ai.google)
- [GitHub](https://github.com)

![AI 概念图](https://via.placeholder.com/400x200/4285f4/ffffff?text=AI+Concept)

---

## 高级格式测试

### 行内代码和特殊字符

这里有一些行内代码：\`const x = 42;\`，还有特殊字符：**粗体**、*斜体*、~~删除线~~。

### 复杂列表

1. 第一级有序列表
   - 第二级无序列表
   - 另一个第二级项目
     1. 第三级有序列表
     2. 另一个第三级项目
        - [x] 第四级任务列表（已完成）
        - [ ] 第四级任务列表（未完成）
2. 回到第一级
3. 最后一个第一级项目

### 代码块语言测试

\`\`\`bash
# Shell 脚本示例
echo "Hello, AI World!"
cd /path/to/project
npm install
npm run build
\`\`\`

\`\`\`json
{
  "name": "ai-project",
  "version": "1.0.0",
  "dependencies": {
    "tensorflow": "^2.0.0",
    "pytorch": "^1.0.0"
  },
  "scripts": {
    "train": "python train.py",
    "test": "python test.py"
  }
}
\`\`\`

\`\`\`yaml
# Docker Compose 配置
version: '3.8'
services:
  ai-service:
    image: tensorflow/tensorflow:latest
    ports:
      - "8080:8080"
    environment:
      - MODEL_PATH=/models
    volumes:
      - ./models:/models
\`\`\`

## 总结

人工智能技术正在快速发展，从 **狭义 AI** 向 **通用 AI** 迈进。我们需要：

1. 持续学习新技术
2. 关注伦理和安全问题
3. 推动 AI 的负责任发展

*让我们一起迎接 AI 时代的到来！* 🚀

---

**注意：** 这个测试页面展示了新的 markdown-it 解析器的各种功能，包括：
- 代码高亮（支持多种语言）
- 任务列表
- 表格
- 引用块
- 锚点链接
- 目录生成
- 各种文本格式`
    }
  }
}
</script>

<style lang="scss" scoped>
.markdown-test {
  padding: 20px;
  max-width: 1600px;
  margin: 0 auto;
}

.test-header {
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid var(--border-color);

  h1 {
    margin: 0 0 15px 0;
    color: var(--text-primary);
    font-size: 1.8em;
  }
}

.controls {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;

  .el-switch {
    margin-right: 0;
  }
}

.test-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  height: calc(100vh - 200px);
}

.input-section, .output-section {
  display: flex;
  flex-direction: column;

  h3 {
    margin: 0 0 10px 0;
    color: var(--primary-color);
    font-size: 1.2em;
  }
}

.rendered-output {
  flex: 1;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 20px;
  background: var(--bg-primary);
  overflow-y: auto;
  font-size: 14px;
  line-height: 1.6;
}

@media (max-width: 1200px) {
  .markdown-test {
    padding: 15px;
  }

  .test-container {
    height: calc(100vh - 180px);
  }
}

@media (max-width: 768px) {
  .test-container {
    grid-template-columns: 1fr;
    height: auto;
  }

  .controls {
    flex-direction: column;
    gap: 10px;
  }

  .rendered-output {
    min-height: 400px;
  }
}
</style>
