import axios from 'axios'
import store from '@/store'
import { Message, MessageBox } from 'element-ui'

/**
 * HTTP请求工具类
 * 基于axios封装，提供统一的请求/响应处理、错误处理、token管理等功能
 */

// 创建axios实例
const service = axios.create({
  baseURL: process.env.VUE_APP_BASE_API || '/api', // API基础路径，从环境变量获取
  timeout: 30000, // 默认请求超时时间30秒
  headers: {
    'Content-Type': 'application/json;charset=UTF-8' // 默认请求头
  }
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 在发送请求之前做些什么
    
    // 添加token到请求头
    const token = store.getters['user/token']
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
    }
    
    // 添加请求时间戳（防止缓存）
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        // _t: Date.now()
      }
    }
    
    // 为AI接口设置更长的超时时间
    if (config.url && config.url.includes('/ai/')) {
      // AI接口设置2分钟超时
      config.timeout = 120000
      console.log('🤖 AI接口检测到，设置超时时间为2分钟:', config.url)
    }

    // 注释：移除全局加载状态显示，改用组件内部状态管理
    // if (config.showLoading !== false) {
    //   store.commit('app/SET_LOADING', true)
    // }

    console.log('API请求:', config.method?.toUpperCase(), config.url, config.params || config.data, `超时:${config.timeout}ms`)
    
    return config
  },
  error => {
    // 对请求错误做些什么
    console.error('请求错误:', error)
    // 注释：移除全局加载状态隐藏，改用组件内部状态管理
    // store.commit('app/SET_LOADING', false)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    // 注释：移除全局加载状态隐藏，改用组件内部状态管理
    // store.commit('app/SET_LOADING', false)
    
    const res = response.data
    console.log('API响应:', response.config.url, res)
    
    // 根据后端约定的响应格式处理
    if (res.code !== undefined) {
      // 如果后端返回的code不是200，则判为错误
      if (res.code !== 200) {
        Message({
          message: res.message || '请求失败',
          type: 'error',
          duration: 5 * 1000
        })
        
        // 401: 未授权，需要重新登录
        if (res.code === 401) {
          MessageBox.confirm(
            '您的登录状态已过期，请重新登录',
            '系统提示',
            {
              confirmButtonText: '重新登录',
              cancelButtonText: '取消',
              type: 'warning'
            }
          ).then(() => {
            store.dispatch('user/logout').then(() => {
              location.reload()
            })
          })
        }
        
        return Promise.reject(new Error(res.message || '请求失败'))
      } else {
        // 请求成功，返回数据
        return res.data
      }
    } else {
      // 如果后端没有统一的响应格式，直接返回数据
      return res
    }
  },
  error => {
    // 注释：移除全局加载状态隐藏，改用组件内部状态管理
    // store.commit('app/SET_LOADING', false)

    console.error('响应错误:', error)

    // 检查是否跳过错误消息显示
    const skipErrorMessage = error.config?.params?._skipErrorMessage ||
                            error.config?.data?._skipErrorMessage ||
                            error.config?._skipErrorMessage

    console.log('🔍 错误处理检查:', {
      skipErrorMessage,
      configParams: error.config?.params,
      configData: error.config?.data,
      config_skipErrorMessage: error.config?._skipErrorMessage,
      errorCode: error.code,
      errorMessage: error.message
    })

    let message = '网络错误'

    if (error.response) {
      // 请求已发出，但服务器响应的状态码不在 2xx 范围内
      const { status, data } = error.response

      switch (status) {
        case 400:
          message = data.message || '请求参数错误'
          break
        case 401:
          message = '未授权，请重新登录'
          // 清除token并跳转到登录页
          if (!skipErrorMessage) {
            store.dispatch('user/logout').then(() => {
              location.reload()
            })
          }
          break
        case 403:
          message = '拒绝访问'
          break
        case 404:
          message = '请求的资源不存在'
          break
        case 500:
          message = '服务器内部错误'
          break
        case 502:
          message = '网关错误'
          break
        case 503:
          message = '服务不可用'
          break
        case 504:
          message = '网关超时'
          break
        default:
          message = data.message || `连接错误${status}`
      }
    } else if (error.request) {
      // 请求已发出，但没有收到响应
      message = '网络连接超时'
    } else {
      // 发送请求时出了点问题
      message = error.message || '请求失败'
    }

    // 特殊处理AI API的错误
    const isAIAPI = error.config?.url?.includes('/ai/singleChatCompletionText') ||
                   error.config?.url?.includes('/ai/multiwheelGenerationResultJson') ||
                   error.config?.url?.includes('/ai/')

    // 检查是否有跳过全局错误处理的标识
    const skipGlobalErrorHandler = error.config?.skipGlobalErrorHandler

    // 只有在不跳过错误消息且不是AI API且没有跳过全局错误处理时才显示
    if (!skipErrorMessage && !isAIAPI && !skipGlobalErrorHandler) {
      Message({
        message,
        type: 'error',
        duration: 5 * 1000
      })
      console.log('显示错误消息:', message)
    } else {
      let reason = '未知原因'
      if (skipGlobalErrorHandler) reason = '跳过全局错误处理'
      else if (isAIAPI) reason = 'AI API错误'
      else if (skipErrorMessage) reason = '跳过错误消息标识'

      console.warn('🚫 跳过错误消息显示:', {
        message,
        skipErrorMessage,
        isAIAPI,
        skipGlobalErrorHandler,
        url: error.config?.url,
        reason
      })
    }

    return Promise.reject(error)
  }
)

export default service
