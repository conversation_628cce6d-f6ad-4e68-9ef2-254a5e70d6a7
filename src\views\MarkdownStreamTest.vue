<template>
  <div class="markdown-stream-test">
    <div class="test-header">
      <h2>Markdown 流式渲染测试</h2>
      <div class="test-controls">
        <el-button @click="startTest" :disabled="isStreaming" type="primary">
          开始测试
        </el-button>
        <el-button @click="stopTest" :disabled="!isStreaming" type="danger">
          停止测试
        </el-button>
        <el-button @click="clearContent" type="info">
          清空内容
        </el-button>
        <el-select v-model="selectedRenderer" placeholder="选择渲染器" style="margin-left: 10px;">
          <el-option label="原版渲染器" value="original"></el-option>
          <el-option label="超高速渲染器" value="ultrafast"></el-option>
          <el-option label="Worker渲染器" value="worker"></el-option>
          <el-option label="原生DOM渲染器" value="native"></el-option>
          <el-option label="自适应渲染器" value="adaptive"></el-option>
          <el-option label="SSE诊断工具" value="diagnostic"></el-option>
        </el-select>
      </div>
    </div>

    <div class="test-content">
      <div class="test-info">
        <div class="info-item">
          <span>状态:</span>
          <el-tag :type="isStreaming ? 'success' : 'info'">
            {{ isStreaming ? '流式输出中' : '已停止' }}
          </el-tag>
        </div>
        <div class="info-item">
          <span>内容长度:</span>
          <span>{{ streamContent.length }} 字符</span>
        </div>
        <div class="info-item">
          <span>渲染次数:</span>
          <span>{{ renderCount }}</span>
        </div>
        <div class="info-item">
          <span>已用时:</span>
          <span>{{ elapsedTime }}s</span>
        </div>
      </div>

      <div class="test-display">
        <div class="raw-content">
          <h3>原始内容</h3>
          <div class="content-box">
            <pre>{{ streamContent }}</pre>
          </div>
        </div>

        <div class="rendered-content">
          <h3>渲染结果 - {{ getRendererName() }}</h3>
          <div class="content-box">
            <!-- 原版渲染器 -->
            <StreamingMarkdownRenderer
              v-if="selectedRenderer === 'original'"
              :content="streamContent"
              :is-streaming="isStreaming"
              :enable-html="true"
              :enable-breaks="true"
              :enable-linkify="true"
              :enable-typographer="true"
              @render-count="updateRenderCount"
            />

            <!-- 超高速渲染器 -->
            <UltraFastMarkdownRenderer
              v-else-if="selectedRenderer === 'ultrafast'"
              :content="streamContent"
              :is-streaming="isStreaming"
              @render-count="updateRenderCount"
            />

            <!-- Worker渲染器 -->
            <WorkerMarkdownRenderer
              v-else-if="selectedRenderer === 'worker'"
              :content="streamContent"
              :is-streaming="isStreaming"
              @render-count="updateRenderCount"
            />

            <!-- 原生DOM渲染器 -->
            <NativeMarkdownRenderer
              v-else-if="selectedRenderer === 'native'"
              :content="streamContent"
              :is-streaming="isStreaming"
              @render-count="updateRenderCount"
            />

            <!-- 自适应渲染器 -->
            <AdaptiveMarkdownRenderer
              v-else-if="selectedRenderer === 'adaptive'"
              :content="streamContent"
              :is-streaming="isStreaming"
              :show-debug-info="true"
              @render-count="updateRenderCount"
            />

            <!-- SSE诊断工具 -->
            <SSEMarkdownDiagnostic
              v-else-if="selectedRenderer === 'diagnostic'"
            />
          </div>
        </div>
      </div>

      <!-- SSE 频率监控面板 -->
      <div class="monitor-panel">
        <SSEFrequencyMonitor />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import StreamingMarkdownRenderer from '@/components/StreamingMarkdownRenderer.vue'
import UltraFastMarkdownRenderer from '@/components/UltraFastMarkdownRenderer.vue'
import WorkerMarkdownRenderer from '@/components/WorkerMarkdownRenderer.vue'
import NativeMarkdownRenderer from '@/components/NativeMarkdownRenderer.vue'
import AdaptiveMarkdownRenderer from '@/components/AdaptiveMarkdownRenderer.vue'
import SSEMarkdownDiagnostic from '@/components/SSEMarkdownDiagnostic.vue'
import SSEFrequencyMonitor from '@/components/SSEFrequencyMonitor.vue'

export default {
  name: 'MarkdownStreamTest',
  components: {
    StreamingMarkdownRenderer,
    UltraFastMarkdownRenderer,
    WorkerMarkdownRenderer,
    NativeMarkdownRenderer,
    AdaptiveMarkdownRenderer,
    SSEMarkdownDiagnostic,
    SSEFrequencyMonitor
  },
  data() {
    return {
      isStreaming: false,
      streamContent: '',
      renderCount: 0,
      elapsedTime: 0,
      timer: null,
      startTime: null,
      selectedRenderer: 'adaptive', // 默认使用自适应渲染器
      
      // 测试用的 Markdown 内容
      testMarkdown: `# AI 助手回复

## 关于人工智能

人工智能（**Artificial Intelligence**，简称 *AI*）是计算机科学的一个分支。

### 主要特点

1. **机器学习**：通过数据训练模型
2. **深度学习**：使用神经网络
3. **自然语言处理**：理解和生成文本

### 代码示例

\`\`\`javascript
function greet(name) {
  return \`Hello, \${name}!\`;
}

console.log(greet('World'));
\`\`\`

### 应用领域

- 图像识别
- 语音识别  
- 自动驾驶
- 智能推荐

> 人工智能正在改变我们的世界，让我们的生活变得更加便利和高效。

### 相关链接

- [OpenAI](https://openai.com)
- [Google AI](https://ai.google)

---

**总结**：AI 技术发展迅速，未来将有更多应用场景。

| 技术 | 应用 | 成熟度 |
|------|------|--------|
| 机器学习 | 数据分析 | 高 |
| 深度学习 | 图像识别 | 高 |
| 强化学习 | 游戏AI | 中 |

这就是关于人工智能的简要介绍。`
    }
  },
  methods: {
    startTest() {
      this.isStreaming = true
      this.streamContent = ''
      this.renderCount = 0
      this.elapsedTime = 0
      this.startTime = Date.now()
      
      // 开始计时
      this.timer = setInterval(() => {
        this.elapsedTime = ((Date.now() - this.startTime) / 1000).toFixed(1)
      }, 100)
      
      // 模拟流式输出
      this.simulateStreaming()
    },
    
    stopTest() {
      this.isStreaming = false
      if (this.timer) {
        clearInterval(this.timer)
        this.timer = null
      }
    },
    
    clearContent() {
      this.streamContent = ''
      this.renderCount = 0
      this.elapsedTime = 0
    },
    
    simulateStreaming() {
      const content = this.testMarkdown
      let index = 0
      
      const addChar = () => {
        if (!this.isStreaming || index >= content.length) {
          this.stopTest()
          return
        }
        
        // 每次添加 1-3 个字符，模拟真实的流式输出
        const chunkSize = Math.floor(Math.random() * 3) + 1
        const chunk = content.slice(index, index + chunkSize)
        this.streamContent += chunk
        index += chunkSize
        
        // 随机延迟 50-200ms，模拟网络延迟
        const delay = Math.floor(Math.random() * 150) + 50
        setTimeout(addChar, delay)
      }
      
      addChar()
    },
    
    updateRenderCount(count) {
      this.renderCount = count
    },

    getRendererName() {
      const names = {
        original: '原版渲染器',
        ultrafast: '超高速渲染器',
        worker: 'Worker渲染器',
        native: '原生DOM渲染器',
        adaptive: '自适应渲染器',
        diagnostic: 'SSE诊断工具'
      }
      return names[this.selectedRenderer] || '未知渲染器'
    }
  },
  
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer)
    }
  }
}
</script>

<style lang="scss" scoped>
.markdown-stream-test {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.monitor-panel {
  margin-top: 20px;
}

.test-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
  
  h2 {
    margin: 0;
    color: #333;
  }
}

.test-controls {
  display: flex;
  gap: 10px;
}

.test-info {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  
  .info-item {
    display: flex;
    align-items: center;
    gap: 8px;
    
    span:first-child {
      font-weight: 600;
      color: #666;
    }
  }
}

.test-display {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  
  h3 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 16px;
  }
}

.content-box {
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 15px;
  background: white;
  min-height: 400px;
  max-height: 600px;
  overflow-y: auto;
  
  pre {
    margin: 0;
    white-space: pre-wrap;
    word-wrap: break-word;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.4;
  }
}

.raw-content .content-box {
  background: #f8f9fa;
}

.rendered-content .content-box {
  background: white;
}
</style>
