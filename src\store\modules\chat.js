import Vue from 'vue'

const state = {
  conversations: [],
  currentConversationId: null,
  messages: {},
  // 配置选项：是否启用后台数据同步
  enableBackendSync: true
  // 注释：移除全局isLoading状态，改用组件内部状态管理
  // isLoading: false
}

const mutations = {
  SET_CONVERSATIONS(state, conversations) {
    state.conversations = conversations
  },
  
  ADD_CONVERSATION(state, conversation) {
    state.conversations.unshift(conversation)
  },
  
  UPDATE_CONVERSATION(state, { id, updates }) {
    const index = state.conversations.findIndex(conv => conv.id === id)
    if (index !== -1) {
      state.conversations.splice(index, 1, { ...state.conversations[index], ...updates })
    }
  },
  
  DELETE_CONVERSATION(state, id) {
    state.conversations = state.conversations.filter(conv => conv.id !== id)
    if (state.messages[id]) {
      delete state.messages[id]
    }
    if (state.currentConversationId === id) {
      state.currentConversationId = null
    }
  },
  
  SET_CURRENT_CONVERSATION(state, id) {
    state.currentConversationId = id
  },
  
  SET_MESSAGES(state, { conversationId, messages }) {
    state.messages = {
      ...state.messages,
      [conversationId]: messages
    }
  },
  
  ADD_MESSAGE(state, { conversationId, message }) {
    if (!state.messages[conversationId]) {
      state.messages[conversationId] = []
    }
    state.messages[conversationId].push(message)
  },

  // 更新消息内容（支持追加模式）- 优化响应式更新
  UPDATE_MESSAGE_CONTENT(state, { conversationId, messageId, content, append = false }) {
    const messages = state.messages[conversationId]
    if (messages) {
      const messageIndex = messages.findIndex(m => m.id === messageId)
      if (messageIndex !== -1) {
        const message = messages[messageIndex]
        const oldContent = message.content || ''

        if (append) {
          // 追加模式：将新内容添加到现有内容后面
          message.content = oldContent + content
        } else {
          // 替换模式：完全替换内容
          message.content = content
        }
        // 更新时间戳
        message.timestamp = new Date().toISOString()

        // 添加更新计数器，强制触发响应式更新
        message.updateCount = (message.updateCount || 0) + 1

        // 多重响应式更新策略
        // 1. 直接修改属性
        Vue.set(message, 'content', message.content)
        Vue.set(message, 'timestamp', message.timestamp)
        Vue.set(message, 'updateCount', message.updateCount)

        // 2. 替换整个消息对象
        Vue.set(messages, messageIndex, { ...message })

        // 3. 强制更新整个消息数组
        Vue.set(state.messages, conversationId, [...messages])

        // 调试信息
        console.log('📝 UPDATE_MESSAGE_CONTENT:', {
          messageId: messageId.substring(0, 8) + '...',
          append,
          oldLength: oldContent.length,
          newLength: message.content.length,
          addedLength: content.length,
          updateCount: message.updateCount,
          timestamp: new Date().toLocaleTimeString()
        })
      }
    }
  },

  // 更新消息状态
  UPDATE_MESSAGE_STATUS(state, { conversationId, messageId, isStreaming, isComplete, isError, isInterrupted, errorMessage }) {
    const messages = state.messages[conversationId]
    if (messages) {
      const message = messages.find(m => m.id === messageId)
      if (message) {
        if (isStreaming !== undefined) message.isStreaming = isStreaming
        if (isComplete !== undefined) message.isComplete = isComplete
        if (isError !== undefined) message.isError = isError
        if (isInterrupted !== undefined) message.isInterrupted = isInterrupted
        if (errorMessage !== undefined) message.errorMessage = errorMessage
        // 更新时间戳
        message.timestamp = new Date().toISOString()
      }
    }
  },

  // 更新消息响应时间
  UPDATE_MESSAGE_RESPONSE_TIME(state, { conversationId, messageId, responseTime }) {
    const messages = state.messages[conversationId]
    if (messages) {
      const message = messages.find(m => m.id === messageId)
      if (message) {
        message.responseTime = responseTime
        console.log('⏱️ 设置消息响应时间:', {
          messageId: messageId.substring(0, 8) + '...',
          responseTime: responseTime,
          formatted: responseTime < 1 ? `${Math.round(responseTime * 1000)}ms` : `${responseTime.toFixed(1)}s`
        })
      }
    }
  },

  // 删除指定消息
  REMOVE_MESSAGE(state, { conversationId, messageId }) {
    const messages = state.messages[conversationId]
    if (messages) {
      const index = messages.findIndex(m => m.id === messageId)
      if (index !== -1) {
        messages.splice(index, 1)
      }
    }
  },

  // 设置后台同步开关
  SET_BACKEND_SYNC(state, enabled) {
    state.enableBackendSync = enabled
  },

  // 注释：移除SET_LOADING mutation，改用组件内部状态管理
  // SET_LOADING(state, loading) {
  //   state.isLoading = loading
  // }
}

const actions = {
  // 从后台加载对话消息
  async loadMessages({ commit }, conversationId) {
    try {
      const { getMessages } = await import('@/api/chat')
      const messages = await getMessages(conversationId)

      // 确保消息格式正确
      const formattedMessages = messages.map(msg => ({
        id: msg.id || 'msg_' + Date.now() + Math.random(),
        type: msg.type || (msg.sender === 'user' ? 'user' : 'ai'),
        content: msg.content || msg.message || '',
        timestamp: msg.timestamp || msg.createdAt || new Date().toISOString(),
        // 后台返回的 responseTime 可能是毫秒单位，需要转换为秒
        responseTime: msg.responseTime ? (
          msg.responseTime > 1000 ? msg.responseTime / 1000 : msg.responseTime
        ) : null,
        isError: msg.isError || false
      }))

      commit('SET_MESSAGES', { conversationId, messages: formattedMessages })
      return formattedMessages
    } catch (error) {
      console.warn('从后台加载消息失败:', error)
      // 如果后台加载失败，返回空数组，不影响用户体验
      commit('SET_MESSAGES', { conversationId, messages: [] })
      return []
    }
  },

  // 创建新对话
  async createConversation({ commit, dispatch, state }) {
    try {
      const { createConversation: createAPI } = await import('@/api/chat')

      console.log('开始调用创建对话API...')
      const response = await createAPI({ title: '新对话', userId: 1 })
      console.log("创建会话接口请求成功，原始响应:", response)

      // 确保返回的数据有正确的结构
      let newConversation = response

      // 如果API返回的数据结构不完整，补充必要字段
      if (!newConversation || typeof newConversation !== 'object') {
        throw new Error('API返回的数据格式不正确')
      }

      if (!newConversation.id) {
        console.warn('API返回的对话数据缺少id字段，使用本地生成的id')
        newConversation.id = 'conv_' + Date.now()
      }

      if (!newConversation.title) {
        newConversation.title = '新对话'
      }

      if (!newConversation.createdAt) {
        newConversation.createdAt = new Date().toISOString()
      }

      if (!newConversation.updatedAt) {
        newConversation.updatedAt = new Date().toISOString()
      }

      console.log('处理后的对话数据:', newConversation)
      console.log('当前conversations状态:', state.conversations)

      commit('ADD_CONVERSATION', newConversation)
      commit('SET_CURRENT_CONVERSATION', newConversation.id)
      commit('SET_MESSAGES', { conversationId: newConversation.id, messages: [] })

      console.log('mutations执行完成，新的conversations状态:', state.conversations)

      // 自动保存
      dispatch('autoSave')

      console.log('新对话创建成功，ID:', newConversation.id)
      return newConversation.id
    } catch (error) {
      console.error('创建对话API调用失败:', error)
      // 如果API失败，回退到本地创建
      const newConversation = {
        id: 'conv_' + Date.now(),
        title: '新对话',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
      console.log("使用本地创建的对话数据:", newConversation)

      commit('ADD_CONVERSATION', newConversation)
      commit('SET_CURRENT_CONVERSATION', newConversation.id)
      commit('SET_MESSAGES', { conversationId: newConversation.id, messages: [] })

      // 自动保存
      dispatch('autoSave')

      console.log('本地对话创建成功，ID:', newConversation.id)
      return newConversation.id
    }
  },
  
  // 发送消息
  async sendMessage({ commit, state, dispatch }, { conversationId, content, skipUserMessage = false, onStreamStart = null, targetMessageId = null }) {
    // 如果不跳过用户消息，则添加用户消息（兼容直接调用store action的情况）
    if (!skipUserMessage) {
      const userMessage = {
        id: 'msg_' + Date.now(),
        type: 'user',
        content,
        timestamp: new Date().toISOString()
      }

      commit('ADD_MESSAGE', { conversationId, message: userMessage })
    }
    // 注释：不再设置全局加载状态，改用组件内的计时显示
    // commit('SET_LOADING', true)

    try {
      // 使用流式AI对话接口
      const { streamChatWithAI } = await import('@/api/chat')

      console.log('🚀 开始流式AI对话，参数:', { message: content, chatId: conversationId, targetMessageId })

      // 创建或使用现有的AI消息
      let aiMessage
      if (targetMessageId) {
        // 重试模式：使用指定的消息ID
        const messages = state.messages[conversationId]
        aiMessage = messages?.find(m => m.id === targetMessageId)
        if (!aiMessage) {
          throw new Error('找不到指定的目标消息')
        }
        // 重置开始时间用于重新计算耗时
        aiMessage.startTime = Date.now()
        console.log('🔄 重试模式：使用现有消息', aiMessage.id)
      } else {
        // 新消息模式：创建新的AI消息占位符
        aiMessage = {
          id: 'msg_' + (Date.now() + 1),
          type: 'ai',
          content: '',
          timestamp: new Date().toISOString(),
          isStreaming: true, // 标记为流式消息
          startTime: Date.now() // 添加开始时间用于计算实时耗时
        }

        // 立即添加空的AI消息占位符
        commit('ADD_MESSAGE', { conversationId, message: aiMessage })
        console.log('📝 新消息模式：创建新消息', aiMessage.id)
      }

      // 开始流式接收
      const eventSource = streamChatWithAI({
        message: content,
        chatId: conversationId,

        // 消息接收回调 - 实时更新消息内容
        onMessage: (data) => {
          console.log('📨 收到流式数据:', data)

          // 第一次收到数据时，调用回调通知开始流式输出
          if (onStreamStart && aiMessage.content === '') {
            onStreamStart()
          }

          // 更新AI消息内容（追加新内容）
          commit('UPDATE_MESSAGE_CONTENT', {
            conversationId,
            messageId: aiMessage.id,
            content: data,
            append: true // 追加模式
          })

          // 立即触发全局事件，不等待 nextTick
          if (typeof window !== 'undefined' && window.Vue) {
            window.Vue.prototype.$bus && window.Vue.prototype.$bus.$emit('force-markdown-update', {
              conversationId,
              messageId: aiMessage.id,
              content: data,
              timestamp: Date.now()
            })
          }

          // 同时使用 nextTick 确保响应式系统同步
          Vue.nextTick(() => {
            // 二次触发，确保组件更新
            if (typeof window !== 'undefined' && window.Vue) {
              window.Vue.prototype.$bus && window.Vue.prototype.$bus.$emit('force-markdown-update-delayed', {
                conversationId,
                messageId: aiMessage.id,
                content: data,
                timestamp: Date.now()
              })
            }
          })
        },

        // 完成回调
        onComplete: (data) => {
          console.log('✅ 流式对话完成:', data)

          // 尝试解析后台返回的数据
          let backendResponseTime = null
          try {
            if (data && typeof data === 'string') {
              const parsedData = JSON.parse(data)
              if (parsedData.responseTime) {
                // 后台返回的可能是毫秒，需要转换为秒
                backendResponseTime = parsedData.responseTime > 1000
                  ? parsedData.responseTime / 1000
                  : parsedData.responseTime
              }
            }
          } catch (e) {
            // 如果解析失败，忽略错误，使用前端计算的时间
          }

          // 计算响应时间（秒）- 优先使用后台返回的时间
          const frontendResponseTimeMs = aiMessage.startTime ? Date.now() - aiMessage.startTime : 0
          const frontendResponseTimeSeconds = frontendResponseTimeMs / 1000
          const finalResponseTime = backendResponseTime || frontendResponseTimeSeconds

          console.log('⏱️ 响应时间计算:', {
            backend: backendResponseTime,
            frontend: frontendResponseTimeSeconds,
            final: finalResponseTime
          })

          // 标记消息完成并设置响应时间
          commit('UPDATE_MESSAGE_STATUS', {
            conversationId,
            messageId: aiMessage.id,
            isStreaming: false,
            isComplete: true,
            isError: false // 确保清除错误状态
          })

          // 设置响应时间
          commit('UPDATE_MESSAGE_RESPONSE_TIME', {
            conversationId,
            messageId: aiMessage.id,
            responseTime: finalResponseTime
          })

          // 更新对话标题（如果是第一条消息）
          const messages = state.messages[conversationId]
          if (messages && messages.length === 2) { // 用户消息 + AI回复
            const title = content.length > 20 ? content.substring(0, 20) + '...' : content
            commit('UPDATE_CONVERSATION', { id: conversationId, updates: { title } })

            // 同步到服务器
            import('@/api/chat').then(({ updateConversation }) => {
              updateConversation(conversationId, { title }).catch(error => {
                console.warn('更新对话标题失败:', error)
              })
            })
          }
        },

        // 错误回调
        onError: (error) => {
          console.error('❌ 流式对话错误:', error)

          // 更新消息为错误状态
          commit('UPDATE_MESSAGE_STATUS', {
            conversationId,
            messageId: aiMessage.id,
            isStreaming: false,
            isError: true,
            errorMessage: error.message || '流式对话失败'
          })

          // 如果没有收到任何内容，显示错误信息
          const currentMessage = state.messages[conversationId]?.find(m => m.id === aiMessage.id)
          if (!currentMessage?.content) {
            commit('UPDATE_MESSAGE_CONTENT', {
              conversationId,
              messageId: aiMessage.id,
              content: '⚠️ AI回复失败，请稍后重试',
              append: false
            })
          }
        }
      })

      // 返回流式对话结果
      return {
        success: true,
        message: aiMessage,
        eventSource,
        isStreaming: true
      }

    } catch (error) {
      console.error('AI API调用失败:', error)
      console.log('🔍 错误详情:', {
        code: error.code,
        message: error.message,
        response: error.response?.status,
        config: error.config?.url
      })

      // 检查错误类型，区分网络超时和真正的API错误
      const isTimeoutError = error.code === 'ECONNABORTED' ||
                            error.code === 'ETIMEDOUT' ||
                            error.message?.includes('timeout') ||
                            error.message?.includes('超时') ||
                            error.message?.includes('exceeded')

      // 检查是否是2分钟超时（AI接口的正常超时时间）
      const isAITimeout = isTimeoutError && error.config?.timeout === 120000

      if (isAITimeout) {
        console.warn('⚠️ AI接口2分钟超时，这是正常的超时保护机制')
        // 对于AI接口的2分钟超时，显示友好提示
        const timeoutMessage = {
          id: 'msg_' + (Date.now() + 1),
          type: 'ai',
          content: '⏱️ **AI响应超时**\n\n您的问题可能比较复杂，AI需要更多时间思考。\n\n💡 **建议**：\n- 请稍等片刻后重试\n- 或者尝试简化您的问题\n- 如果问题持续，请联系技术支持',
          timestamp: new Date().toISOString(),
          isTimeout: true
        }

        commit('ADD_MESSAGE', { conversationId, message: timeoutMessage })
        return { success: true, message: timeoutMessage, isTimeout: true }
      } else if (isTimeoutError) {
        console.warn('⚠️ 其他接口超时错误')
        // 其他类型的超时错误，回退到本地模拟
      }

      // 如果是其他类型的API错误，回退到本地模拟
      try {
        console.log('🔄 回退到本地模拟回复')
        const aiResponse = await generateAIResponse(content)

        const localAiMessage = {
          id: 'msg_' + (Date.now() + 1),
          type: 'ai',
          content: aiResponse,
          timestamp: new Date().toISOString()
        }

        commit('ADD_MESSAGE', { conversationId, message: localAiMessage })

        // 更新对话标题（本地模拟回复）
        const messages = state.messages[conversationId]
        if (messages && messages.length === 2) {
          const title = content.length > 20 ? content.substring(0, 20) + '...' : content
          commit('UPDATE_CONVERSATION', { id: conversationId, updates: { title } })
        }

        // 返回本地模拟结果
        return { success: true, message: localAiMessage, isLocal: true }

      } catch (localError) {
        console.error('本地模拟也失败:', localError)
        // 发送错误消息
        const errorMessage = {
          id: 'msg_' + (Date.now() + 1),
          type: 'ai',
          content: '抱歉，我现在无法回复您的消息。请检查网络连接或稍后再试。',
          timestamp: new Date().toISOString(),
          isError: true
        }
        commit('ADD_MESSAGE', { conversationId, message: errorMessage })

        // 返回错误结果
        return { success: false, message: errorMessage, error: localError }
      }
    } finally {
      // 注释：不再设置全局加载状态，改用组件内的计时显示
      // commit('SET_LOADING', false)
      // 自动保存
      dispatch('autoSave')
    }
  },
  
  // 删除对话
  async deleteConversation({ commit, dispatch }, id) {
    try {
      const { deleteConversation: deleteAPI } = await import('@/api/chat')
      await deleteAPI(id)
    } catch (error) {
      console.warn('删除对话API调用失败:', error)
    } finally {
      commit('DELETE_CONVERSATION', id)
      dispatch('autoSave')
    }
  },

  

  // 重命名对话
  async renameConversation({ commit, dispatch }, { id, title }) {
    try {
      const { updateConversation } = await import('@/api/chat')
      await updateConversation(id, { title })
    } catch (error) {
      console.warn('重命名对话API调用失败:', error)
    } finally {
      commit('UPDATE_CONVERSATION', { id, updates: { title } })
      dispatch('autoSave')
    }
  },
  
  // 加载对话历史
  async loadConversations({ commit }) {
    try {
      // 首先尝试从服务器加载
      const { getConversations, getMessages } = await import('@/api/chat')

      const conversations = await getConversations()
      commit('SET_CONVERSATIONS', conversations)

      // 加载每个对话的消息
      for (const conversation of conversations) {
        try {
          const messages = await getMessages(conversation.id)
          commit('SET_MESSAGES', { conversationId: conversation.id, messages })
        } catch (error) {
          console.warn(`加载对话 ${conversation.id} 的消息失败:`, error)
        }
      }

    } catch (error) {
      console.warn('从服务器加载对话失败，使用本地数据:', error)

      // 如果API失败，从本地存储加载
      const savedConversations = JSON.parse(localStorage.getItem('conversations') || '[]')
      const savedMessages = JSON.parse(localStorage.getItem('messages') || '{}')

      // 如果是首次使用，创建示例对话
      if (savedConversations.length === 0) {
        const exampleConversation = {
          id: 'example_' + Date.now(),
          title: '欢迎使用AI助手',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }

        const exampleMessages = [
          {
            id: 'welcome_1',
            type: 'ai',
            content: '👋 欢迎使用AI助手！\n\n我是您的智能对话伙伴，可以帮助您：\n\n🤖 **回答各种问题** - 技术、学习、生活等\n💡 **提供建议和想法** - 解决问题的思路\n📚 **学习指导** - 编程、技术学习路径\n🔧 **技术支持** - 开发相关问题\n\n请随时向我提问，我会尽力为您提供有用的回答！',
            timestamp: new Date().toISOString()
          }
        ]

        commit('SET_CONVERSATIONS', [exampleConversation])
        commit('SET_MESSAGES', { conversationId: exampleConversation.id, messages: exampleMessages })

        // 保存到本地存储
        localStorage.setItem('conversations', JSON.stringify([exampleConversation]))
        localStorage.setItem('messages', JSON.stringify({ [exampleConversation.id]: exampleMessages }))
      } else {
        commit('SET_CONVERSATIONS', savedConversations)
        Object.keys(savedMessages).forEach(conversationId => {
          commit('SET_MESSAGES', { conversationId, messages: savedMessages[conversationId] })
        })
      }
    }
  },
  
  // 保存到本地存储
  saveToLocal({ state }) {
    try {
      localStorage.setItem('conversations', JSON.stringify(state.conversations))
      localStorage.setItem('messages', JSON.stringify(state.messages))
    } catch (error) {
      console.error('保存数据到本地存储失败:', error)
    }
  },

  // 自动保存（防抖）
  autoSave: (() => {
    let timer = null
    return function({ dispatch }) {
      clearTimeout(timer)
      timer = setTimeout(() => {
        dispatch('saveToLocal')
      }, 1000)
    }
  })()
}

const getters = {
  conversations: state => state.conversations,
  currentConversation: state => {
    return state.conversations.find(conv => conv.id === state.currentConversationId)
  },
  currentMessages: state => {
    return state.messages[state.currentConversationId] || []
  },
  // 注释：移除isLoading getter，改用组件内部状态管理
  // isLoading: state => state.isLoading
}

// AI回复生成函数
async function generateAIResponse(userMessage) {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, Math.random() * 2000 + 1000))

  const message = userMessage.toLowerCase()

  // 智能回复模板
  const responses = {
    // 问候语
    greetings: [
      '你好！我是AI助手，很高兴为您服务。有什么我可以帮助您的吗？',
      '您好！我是您的AI助手，随时准备为您解答问题。',
      '嗨！欢迎使用AI助手，我会尽力帮助您解决问题。'
    ],

    // 编程相关
    programming: [
      '关于编程问题，我可以为您提供详细的解答。请告诉我您具体遇到了什么问题？',
      '编程是一个很有趣的领域！我可以帮您解释概念、调试代码或推荐最佳实践。',
      '我很乐意帮助您解决编程问题。请分享更多细节，我会提供针对性的建议。'
    ],

    // 技术相关
    technology: [
      '技术发展日新月异，我会尽力为您提供最新、最准确的信息。',
      '关于技术问题，我可以从多个角度为您分析，包括原理、应用和发展趋势。',
      '技术问题往往需要深入理解，让我为您详细解释相关概念。'
    ],

    // 学习相关
    learning: [
      '学习是一个持续的过程，我很高兴能够陪伴您的学习之旅。',
      '每个人的学习方式不同，我会根据您的需求提供个性化的建议。',
      '学习新知识确实需要时间和耐心，我会尽力让这个过程更加高效。'
    ],

    // 默认回复
    default: [
      '这是一个很有趣的问题！让我仔细思考一下如何为您提供最好的答案。',
      '我理解您的问题。基于我的知识，我认为可以从以下几个方面来分析：',
      '感谢您的提问！这让我想到了几个相关的要点，让我为您详细说明。',
      '您提出了一个很好的问题。让我结合相关知识为您提供一个全面的回答。'
    ]
  }

  // 关键词匹配
  if (message.includes('你好') || message.includes('hello') || message.includes('hi')) {
    return getRandomResponse(responses.greetings)
  }

  if (message.includes('编程') || message.includes('代码') || message.includes('程序') ||
      message.includes('javascript') || message.includes('python') || message.includes('vue') ||
      message.includes('react') || message.includes('代码')) {
    return getRandomResponse(responses.programming) + '\n\n' + generateProgrammingAdvice(message)
  }

  if (message.includes('技术') || message.includes('科技') || message.includes('ai') ||
      message.includes('人工智能') || message.includes('算法')) {
    return getRandomResponse(responses.technology) + '\n\n' + generateTechAdvice(message)
  }

  if (message.includes('学习') || message.includes('教程') || message.includes('如何') ||
      message.includes('怎么') || message.includes('方法')) {
    return getRandomResponse(responses.learning) + '\n\n' + generateLearningAdvice(message)
  }

  // 默认智能回复
  return getRandomResponse(responses.default) + '\n\n' + generateContextualResponse(userMessage)
}

function getRandomResponse(responses) {
  return responses[Math.floor(Math.random() * responses.length)]
}

function generateProgrammingAdvice(message) {
  const advice = [
    '💡 **编程小贴士**：\n- 保持代码简洁和可读性\n- 多写注释，方便后续维护\n- 定期重构代码，提高代码质量',
    '🔧 **开发建议**：\n- 使用版本控制系统（如Git）\n- 编写单元测试确保代码质量\n- 遵循团队的编码规范',
    '📚 **学习路径**：\n- 掌握基础语法和概念\n- 通过实际项目练习\n- 阅读优秀的开源代码'
  ]
  return getRandomResponse(advice)
}

function generateTechAdvice(message) {
  const advice = [
    '🚀 **技术趋势**：\n- 人工智能和机器学习持续发展\n- 云计算和边缘计算成为主流\n- 低代码/无代码平台兴起',
    '⚡ **技术选型**：\n- 根据项目需求选择合适的技术栈\n- 考虑团队技能和学习成本\n- 关注技术的生态系统和社区支持',
    '🔍 **技术学习**：\n- 关注官方文档和最佳实践\n- 参与开源项目和技术社区\n- 持续学习和实践新技术'
  ]
  return getRandomResponse(advice)
}

function generateLearningAdvice(message) {
  const advice = [
    '📖 **学习方法**：\n- 制定明确的学习目标\n- 理论学习与实践相结合\n- 定期总结和复习知识点',
    '🎯 **学习策略**：\n- 从基础开始，循序渐进\n- 多做练习和项目实战\n- 寻找学习伙伴，互相交流',
    '💪 **学习习惯**：\n- 保持持续学习的习惯\n- 记录学习笔记和心得\n- 及时应用所学知识'
  ]
  return getRandomResponse(advice)
}

function generateContextualResponse(userMessage) {
  const contextResponses = [
    `针对您提到的"${userMessage.substring(0, 20)}${userMessage.length > 20 ? '...' : ''}"，我建议您可以从以下角度思考：\n\n1. 首先明确问题的核心\n2. 分析可能的解决方案\n3. 评估各方案的优缺点\n4. 选择最适合的方案实施`,

    `关于您的问题，我认为可以这样理解：\n\n这类问题通常需要综合考虑多个因素。建议您：\n- 收集更多相关信息\n- 咨询专业人士的意见\n- 结合实际情况做出判断`,

    `您的问题很有价值！让我为您提供一些思路：\n\n🔸 **分析角度**：从不同维度审视问题\n🔸 **解决思路**：采用系统性的方法\n🔸 **实施建议**：制定具体的行动计划`,

    `基于您的描述，我想分享一些见解：\n\n这个话题确实值得深入探讨。我建议您可以：\n• 查阅相关资料和案例\n• 与有经验的人交流\n• 通过实践验证想法`
  ]

  return getRandomResponse(contextResponses)
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
