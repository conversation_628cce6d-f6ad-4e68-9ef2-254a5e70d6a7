{"name": "demo1-ai-ui", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"axios": "^1.6.0", "element-ui": "^2.15.14", "highlight.js": "^11.11.1", "markdown-it": "^14.1.0", "markdown-it-anchor": "^9.2.0", "markdown-it-highlightjs": "^4.2.0", "markdown-it-table-of-contents": "^0.9.0", "markdown-it-task-lists": "^2.1.1", "marked": "^4.3.0", "vue": "^2.6.14", "vue-markdown-render": "^2.2.1", "vue-router": "^3.5.4", "vuex": "^3.6.2"}, "devDependencies": {"@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "babel-eslint": "^10.1.0", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "vue-template-compiler": "^2.6.14", "sass": "^1.32.7", "sass-loader": "^12.0.0"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}