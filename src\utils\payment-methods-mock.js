/**
 * 支付方式模拟数据
 * 用于测试从后台获取支付方式的功能
 */

export const mockPaymentMethods = [
  {
    id: 'alipay',
    name: '支付宝',
    description: '安全快捷，支持花呗分期',
    icon: 'el-icon-mobile-phone',
    image: '/src/assets/alipay.png',
    recommended: true,
    disabled: false,
    order: 1
  },
  {
    id: 'wechat',
    name: '微信支付',
    description: '便捷支付，微信用户首选',
    icon: 'el-icon-chat-dot-round',
    image: '/src/assets/wechat.jpeg',
    recommended: false,
    disabled: false,
    order: 2
  },
  {
    id: 'balance',
    name: '余额支付',
    description: '使用账户余额支付',
    icon: 'el-icon-wallet',
    image: null,
    recommended: false,
    disabled: false,
    order: 3
  },
  {
    id: 'bank',
    name: '银行卡',
    description: '支持各大银行储蓄卡',
    icon: 'el-icon-bank-card',
    image: null,
    recommended: false,
    disabled: false,
    order: 4
  },
  {
    id: 'paypal',
    name: 'PayPal',
    description: '国际支付，支持外币',
    icon: 'el-icon-money',
    image: null,
    recommended: false,
    disabled: true,
    disabledReason: '暂时维护中',
    order: 5
  }
]

/**
 * 模拟API响应格式
 */
export const mockApiResponse = {
  code: 200,
  message: 'success',
  data: mockPaymentMethods
}
