// Markdown 渲染 Web Worker
// 在后台线程中处理 Markdown 渲染，避免阻塞主线程

// 尝试导入 markdown-it
let MarkdownIt = null

// 在 Worker 中导入 markdown-it
try {
  // 尝试从 CDN 导入
  importScripts('https://cdn.jsdelivr.net/npm/markdown-it@13.0.1/dist/markdown-it.min.js')
  MarkdownIt = markdownit
} catch (e) {
  console.error('Worker: 无法从 CDN 加载 markdown-it:', e)
  
  // 备用方案：使用简单的 Markdown 解析
  MarkdownIt = null
}

// 初始化 Markdown 实例
let md = null

if (MarkdownIt) {
  md = new MarkdownIt({
    html: true,
    xhtmlOut: false,
    breaks: true,
    linkify: true,
    typographer: true
  })
}

// 简单的 Markdown 解析器（备用方案）
function simpleMarkdownParse(text) {
  if (!text) return ''
  
  return text
    // 标题
    .replace(/^### (.*$)/gim, '<h3>$1</h3>')
    .replace(/^## (.*$)/gim, '<h2>$1</h2>')
    .replace(/^# (.*$)/gim, '<h1>$1</h1>')
    
    // 粗体和斜体
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    
    // 代码块
    .replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>')
    .replace(/`(.*?)`/g, '<code>$1</code>')
    
    // 链接
    .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2">$1</a>')
    
    // 列表
    .replace(/^\* (.*$)/gim, '<li>$1</li>')
    .replace(/(<li>.*<\/li>)/s, '<ul>$1</ul>')
    
    // 换行
    .replace(/\n/g, '<br>')
}

// 处理消息
self.onmessage = function(e) {
  const { id, content, options } = e.data
  
  try {
    let rendered = ''
    
    if (md) {
      // 使用 markdown-it 渲染
      const startTime = performance.now()
      rendered = md.render(content || '')
      const renderTime = performance.now() - startTime
      
      // 发送结果
      self.postMessage({
        id,
        success: true,
        rendered,
        renderTime,
        method: 'markdown-it'
      })
    } else {
      // 使用简单解析器
      const startTime = performance.now()
      rendered = simpleMarkdownParse(content || '')
      const renderTime = performance.now() - startTime
      
      // 发送结果
      self.postMessage({
        id,
        success: true,
        rendered,
        renderTime,
        method: 'simple'
      })
    }
    
  } catch (error) {
    // 发送错误
    self.postMessage({
      id,
      success: false,
      error: error.message,
      rendered: content ? content.replace(/\n/g, '<br>') : ''
    })
  }
}
