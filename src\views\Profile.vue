<template>
  <div class="profile-container">
    <div class="profile-header">
      <h2 class="profile-title">
        <i class="el-icon-user"></i>
        个人资料
      </h2>
    </div>
    
    <div class="profile-content">
      <div class="profile-main">
        <!-- 头像区域 -->
        <div class="avatar-section">
          <div class="avatar-container">
            <div class="user-avatar-large">
              {{ userInfo.username ? userInfo.username.charAt(0).toUpperCase() : 'U' }}
            </div>
            <el-button
              type="text"
              class="avatar-edit-btn"
              @click="changeAvatar"
            >
              <i class="el-icon-camera"></i>
            </el-button>
          </div>
        </div>
        
        <!-- 用户信息表单 -->
        <div class="form-section">
          <el-form
            ref="profileForm"
            :model="profileForm"
            :rules="profileRules"
            label-width="80px"
            class="profile-form"
          >
            <el-form-item label="用户名" prop="username">
              <el-input
                v-model="profileForm.username"
                placeholder="请输入用户名"
              />
            </el-form-item>
            
            <el-form-item label="邮箱" prop="email">
              <el-input
                v-model="profileForm.email"
                placeholder="请输入邮箱地址"
                disabled
              />
            </el-form-item>
            
            <el-form-item label="个人简介" prop="bio">
              <el-input
                v-model="profileForm.bio"
                type="textarea"
                :rows="3"
                placeholder="介绍一下自己..."
                maxlength="200"
                show-word-limit
              />
            </el-form-item>
          </el-form>
        </div>
        
        <!-- 统计信息 -->
        <div class="stats-section">
          <h3 class="stats-title">使用统计</h3>
          <div class="stats-grid">
            <div class="stat-card">
              <div class="stat-icon">
                <i class="el-icon-chat-dot-round"></i>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ stats.conversationCount }}</div>
                <div class="stat-label">对话次数</div>
              </div>
            </div>
            
            <div class="stat-card">
              <div class="stat-icon">
                <i class="el-icon-message"></i>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ stats.messageCount }}</div>
                <div class="stat-label">消息数量</div>
              </div>
            </div>
            
            <div class="stat-card">
              <div class="stat-icon">
                <i class="el-icon-time"></i>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ stats.usageDays }}</div>
                <div class="stat-label">使用天数</div>
              </div>
            </div>
            
            <div class="stat-card">
              <div class="stat-icon">
                <i class="el-icon-star-on"></i>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ stats.favoriteCount }}</div>
                <div class="stat-label">收藏对话</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="profile-footer">
      <el-button @click="resetForm">重置</el-button>
      <el-button
        type="primary"
        :loading="saving"
        @click="saveProfile"
      >
        <i class="el-icon-check"></i>
        保存资料
      </el-button>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'

export default {
  name: 'Profile',
  data() {
    return {
      saving: false,
      profileForm: {
        username: '',
        email: '',
        bio: ''
      },
      profileRules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
          { min: 2, max: 20, message: '用户名长度在 2 到 20 个字符', trigger: 'blur' }
        ],
        email: [
          { required: true, message: '请输入邮箱地址', trigger: 'blur' },
          { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
        ],
        bio: [
          { max: 200, message: '个人简介不能超过200个字符', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    ...mapGetters('user', ['userInfo']),
    ...mapGetters('chat', ['conversations']),
    
    stats() {
      // 使用模拟的统计数据
      return {
        conversationCount: this.conversations.length || 0,
        messageCount: Object.values(this.$store.state.chat.messages || {})
          .reduce((total, messages) => total + messages.length, 0),
        usageDays: Math.max(1, Math.floor((Date.now() - new Date('2024-01-01').getTime()) / (1000 * 60 * 60 * 24))),
        favoriteCount: Math.floor((this.conversations.length || 0) * 0.3)
      }
    }
  },
  created() {
    this.initForm()
  },
  methods: {
    ...mapActions('user', ['updateUserInfo']),
    
    initForm() {
      this.profileForm = {
        username: this.userInfo.username || '',
        email: this.userInfo.email || '',
        bio: this.userInfo.bio || ''
      }
    },
    
    resetForm() {
      this.initForm()
      this.$refs.profileForm.clearValidate()
    },
    
    async saveProfile() {
      try {
        const valid = await this.$refs.profileForm.validate()
        if (!valid) return
        
        this.saving = true
        
        // 模拟保存延迟
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        this.updateUserInfo(this.profileForm)
        this.$message.success('个人资料已更新')
      } catch (error) {
        this.$message.error('保存失败，请重试')
      } finally {
        this.saving = false
      }
    },
    
    changeAvatar() {
      this.$message.info('头像上传功能开发中...')
      // 这里可以实现头像上传功能
    }
  }
}
</script>

<style lang="scss" scoped>
.profile-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--bg-primary);
}

.profile-header {
  padding: 20px;
  border-bottom: 1px solid var(--border-color);
}

.profile-title {
  font-size: var(--font-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.profile-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.profile-main {
  max-width: 800px;
  margin: 0 auto;
}

.avatar-section {
  text-align: center;
  margin-bottom: 40px;
}

.avatar-container {
  position: relative;
  display: inline-block;
}

.user-avatar-large {
  width: 100px;
  height: 100px;
  background: var(--primary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 36px;
  font-weight: 600;
  margin: 0 auto 16px;
}

.avatar-edit-btn {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--bg-tertiary);
  border: 2px solid var(--bg-primary);
  padding: 0;
  
  &:hover {
    background: var(--text-secondary);
    color: white;
  }
}

.form-section {
  margin-bottom: 40px;
}

.profile-form {
  max-width: 500px;
  margin: 0 auto;
}

.stats-section {
  margin-bottom: 40px;
}

.stats-title {
  font-size: var(--font-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 20px;
  text-align: center;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.stat-card {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
  }
}

.stat-icon {
  font-size: 32px;
  color: var(--primary-color);
  margin-bottom: 12px;
}

.stat-number {
  font-size: var(--font-2xl);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.stat-label {
  font-size: var(--font-sm);
  color: var(--text-secondary);
}

.profile-footer {
  padding: 20px;
  border-top: 1px solid var(--border-color);
  text-align: center;
  display: flex;
  justify-content: center;
  gap: 12px;
}
</style>
