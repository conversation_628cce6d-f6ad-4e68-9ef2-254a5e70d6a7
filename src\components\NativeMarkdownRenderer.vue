<template>
  <div class="native-markdown-renderer" ref="container">
    <!-- 使用原生 DOM 操作，完全绕过 Vue 的响应式系统 -->
    <div ref="content" class="markdown-content"></div>
  </div>
</template>

<script>
// 导入 MarkdownIt
import MarkdownIt from 'markdown-it'

/**
 * 原生 DOM 操作的超高速 Markdown 渲染器
 * 完全绕过 Vue 的响应式系统，直接操作 DOM
 */
export default {
  name: 'NativeMarkdownRenderer',
  props: {
    content: {
      type: String,
      default: ''
    },
    isStreaming: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      md: null,
      renderCount: 0,
      lastContent: '',
      lastRenderedLength: 0,
      renderTimer: null,
      pendingContent: null
    }
  },
  created() {
    this.initMarkdown()
  },
  mounted() {
    // 立即渲染
    this.renderContentNative()
    
    // 监听全局事件
    if (this.$bus) {
      this.$bus.$on('force-markdown-update', this.handleForceUpdate)
      this.$bus.$on('force-markdown-update-delayed', this.handleForceUpdate)
    }
    
    // 设置高频渲染
    this.setupHighFrequencyRender()
  },
  beforeDestroy() {
    this.cleanup()
  },
  watch: {
    content: {
      handler(newContent) {
        this.pendingContent = newContent
        if (this.isStreaming) {
          // 立即渲染
          this.renderContentNative()
        }
      },
      immediate: true
    },
    
    isStreaming: {
      handler(newVal) {
        if (newVal) {
          this.setupHighFrequencyRender()
        } else {
          this.stopHighFrequencyRender()
          // 最后一次渲染
          this.renderContentNative()
        }
      }
    }
  },
  methods: {
    // 初始化 Markdown
    initMarkdown() {
      try {
        if (MarkdownIt) {
          this.md = new MarkdownIt({
            html: true,
            xhtmlOut: false,
            breaks: true,
            linkify: true,
            typographer: true
          })
        } else if (window.markdownit) {
          this.md = window.markdownit({
            html: true,
            xhtmlOut: false,
            breaks: true,
            linkify: true,
            typographer: true
          })
        }
        console.log('✅ NativeMarkdownRenderer: Markdown-it 初始化成功')
      } catch (error) {
        console.error('❌ NativeMarkdownRenderer: 初始化失败:', error)
        this.md = null
      }
    },
    
    // 设置高频渲染
    setupHighFrequencyRender() {
      this.stopHighFrequencyRender()
      
      // 使用 requestAnimationFrame 实现最高频率的渲染
      const renderLoop = () => {
        if (this.isStreaming) {
          this.renderContentNative()
          this.renderTimer = requestAnimationFrame(renderLoop)
        }
      }
      
      this.renderTimer = requestAnimationFrame(renderLoop)
    },
    
    // 停止高频渲染
    stopHighFrequencyRender() {
      if (this.renderTimer) {
        cancelAnimationFrame(this.renderTimer)
        this.renderTimer = null
      }
    },
    
    // 原生 DOM 渲染方法
    renderContentNative() {
      const content = this.pendingContent !== null ? this.pendingContent : this.content
      
      // 如果内容没有变化，跳过渲染
      if (content === this.lastContent) {
        return
      }
      
      const contentElement = this.$refs.content
      if (!contentElement) {
        return
      }
      
      try {
        const startTime = performance.now()
        
        let rendered = ''
        
        if (this.md && content) {
          // 使用 markdown-it 渲染
          rendered = this.md.render(content)
        } else if (content) {
          // 简单的文本处理
          rendered = this.simpleMarkdownParse(content)
        }
        
        // 直接设置 innerHTML，绕过 Vue
        contentElement.innerHTML = rendered
        
        // 更新状态
        this.lastContent = content
        this.lastRenderedLength = rendered.length
        this.renderCount++
        this.pendingContent = null
        
        const renderTime = performance.now() - startTime
        
        if (this.isStreaming) {
          console.log(`⚡ Native渲染: ${content?.length || 0} 字符, 耗时 ${renderTime.toFixed(2)}ms, 第${this.renderCount}次`)
        }
        
        // 发射事件
        this.$emit('render-count', this.renderCount)
        
      } catch (error) {
        console.error('❌ Native渲染失败:', error)
        if (contentElement) {
          contentElement.innerHTML = content ? content.replace(/\n/g, '<br>') : ''
        }
      }
    },
    
    // 简单的 Markdown 解析（备用方案）
    simpleMarkdownParse(text) {
      if (!text) return ''
      
      return text
        // 标题
        .replace(/^### (.*$)/gim, '<h3>$1</h3>')
        .replace(/^## (.*$)/gim, '<h2>$1</h2>')
        .replace(/^# (.*$)/gim, '<h1>$1</h1>')
        
        // 粗体和斜体
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
        
        // 代码块
        .replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>')
        .replace(/`(.*?)`/g, '<code>$1</code>')
        
        // 链接
        .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank">$1</a>')
        
        // 列表项
        .replace(/^\* (.*$)/gim, '<li>$1</li>')
        .replace(/^- (.*$)/gim, '<li>$1</li>')
        .replace(/^\d+\. (.*$)/gim, '<li>$1</li>')
        
        // 引用
        .replace(/^> (.*$)/gim, '<blockquote>$1</blockquote>')
        
        // 换行
        .replace(/\n\n/g, '</p><p>')
        .replace(/\n/g, '<br>')
        .replace(/^(.*)$/, '<p>$1</p>')
    },
    
    // 处理强制更新事件
    handleForceUpdate(eventData) {
      if (this.isStreaming) {
        this.renderContentNative()
      }
    },
    
    // 清理资源
    cleanup() {
      this.stopHighFrequencyRender()
      
      if (this.$bus) {
        this.$bus.$off('force-markdown-update', this.handleForceUpdate)
        this.$bus.$off('force-markdown-update-delayed', this.handleForceUpdate)
      }
    }
  }
}
</script>

<style scoped>
.native-markdown-renderer {
  line-height: 1.6;
  color: var(--text-primary, #333);
  word-wrap: break-word;
  font-size: 14px;
}

.markdown-content {
  min-height: 20px;
}

/* 全局 Markdown 样式 */
.native-markdown-renderer >>> h1,
.native-markdown-renderer >>> h2,
.native-markdown-renderer >>> h3,
.native-markdown-renderer >>> h4,
.native-markdown-renderer >>> h5,
.native-markdown-renderer >>> h6 {
  color: #333;
  margin: 16px 0 12px 0;
  font-weight: 600;
  line-height: 1.4;
}

.native-markdown-renderer >>> h1 {
  font-size: 1.8em;
  border-bottom: 2px solid #eee;
  padding-bottom: 8px;
}

.native-markdown-renderer >>> h2 {
  font-size: 1.5em;
  border-bottom: 1px solid #eee;
  padding-bottom: 6px;
}

.native-markdown-renderer >>> h3 {
  font-size: 1.3em;
  color: #409eff;
}

.native-markdown-renderer >>> p {
  margin: 12px 0;
  line-height: 1.7;
}

.native-markdown-renderer >>> ul,
.native-markdown-renderer >>> ol {
  margin: 12px 0;
  padding-left: 20px;
}

.native-markdown-renderer >>> li {
  margin: 6px 0;
  line-height: 1.6;
}

.native-markdown-renderer >>> strong {
  font-weight: 700;
  color: #409eff;
}

.native-markdown-renderer >>> em {
  font-style: italic;
  color: #666;
}

.native-markdown-renderer >>> code {
  background: rgba(64, 158, 255, 0.1);
  color: #409eff;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Courier New', Monaco, monospace;
  font-size: 0.9em;
}

.native-markdown-renderer >>> pre {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
  margin: 16px 0;
  overflow-x: auto;
  font-family: 'Courier New', Monaco, monospace;
  font-size: 0.9em;
  line-height: 1.4;
}

.native-markdown-renderer >>> pre code {
  background: none;
  color: #333;
  padding: 0;
  border-radius: 0;
}

.native-markdown-renderer >>> blockquote {
  margin: 16px 0;
  padding: 12px 16px;
  border-left: 4px solid #409eff;
  background: rgba(64, 158, 255, 0.05);
  border-radius: 0 8px 8px 0;
  font-style: italic;
  color: #666;
}

.native-markdown-renderer >>> table {
  width: 100%;
  border-collapse: collapse;
  margin: 16px 0;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
}

.native-markdown-renderer >>> th,
.native-markdown-renderer >>> td {
  padding: 12px 16px;
  border-bottom: 1px solid #e9ecef;
  text-align: left;
}

.native-markdown-renderer >>> th {
  background: rgba(64, 158, 255, 0.1);
  font-weight: 600;
  color: #409eff;
}

.native-markdown-renderer >>> a {
  color: #409eff;
  text-decoration: none;
}

.native-markdown-renderer >>> a:hover {
  text-decoration: underline;
}
</style>
