<template>
  <div class="sse-frequency-monitor">
    <div class="monitor-header">
      <h4>📊 SSE 频率实时监控</h4>
      <div class="monitor-controls">
        <el-switch
          v-model="isMonitoring"
          @change="toggleMonitoring"
          active-text="监控中"
          inactive-text="已停止"
        />
        <el-button @click="clearData" size="mini" type="info">清空</el-button>
      </div>
    </div>

    <div class="monitor-content">
      <!-- 实时指标 -->
      <div class="metrics-grid">
        <div class="metric-card">
          <div class="metric-label">SSE 频率</div>
          <div class="metric-value" :class="getFrequencyClass(realTimeStats.sseFrequency)">
            {{ realTimeStats.sseFrequency }} Hz
          </div>
        </div>
        
        <div class="metric-card">
          <div class="metric-label">平均间隔</div>
          <div class="metric-value">
            {{ realTimeStats.averageInterval }} ms
          </div>
        </div>
        
        <div class="metric-card">
          <div class="metric-label">渲染延迟</div>
          <div class="metric-value" :class="getDelayClass(realTimeStats.renderDelay)">
            {{ realTimeStats.renderDelay }} ms
          </div>
        </div>
        
        <div class="metric-card">
          <div class="metric-label">推荐模式</div>
          <div class="metric-value recommended-mode">
            {{ getRecommendedMode(realTimeStats.sseFrequency) }}
          </div>
        </div>
      </div>

      <!-- 频率图表 -->
      <div class="frequency-chart">
        <div class="chart-header">
          <span>频率变化趋势</span>
          <span class="chart-range">最近 {{ chartData.length }} 秒</span>
        </div>
        <div class="chart-container">
          <svg ref="chart" width="100%" height="100" viewBox="0 0 400 100">
            <!-- 网格线 -->
            <defs>
              <pattern id="grid" width="20" height="10" patternUnits="userSpaceOnUse">
                <path d="M 20 0 L 0 0 0 10" fill="none" stroke="#e0e0e0" stroke-width="0.5"/>
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#grid)" />
            
            <!-- 频率曲线 -->
            <polyline
              :points="getChartPoints()"
              fill="none"
              stroke="#409eff"
              stroke-width="2"
            />
            
            <!-- 阈值线 -->
            <line x1="0" y1="20" x2="400" y2="20" stroke="#f56c6c" stroke-width="1" stroke-dasharray="5,5" />
            <text x="5" y="18" font-size="10" fill="#f56c6c">20Hz</text>
            
            <line x1="0" y1="80" x2="400" y2="80" stroke="#e6a23c" stroke-width="1" stroke-dasharray="5,5" />
            <text x="5" y="78" font-size="10" fill="#e6a23c">5Hz</text>
          </svg>
        </div>
      </div>

      <!-- 优化建议 -->
      <div v-if="currentRecommendations.length > 0" class="recommendations">
        <h5>💡 实时优化建议</h5>
        <div 
          v-for="(rec, index) in currentRecommendations" 
          :key="index"
          :class="['recommendation-item', rec.type]"
        >
          <div class="rec-title">{{ rec.title }}</div>
          <div class="rec-message">{{ rec.message }}</div>
          <div class="rec-solution">解决方案: {{ rec.solution }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { sseAnalyzer } from '@/utils/sseFrequencyAnalyzer.js'

export default {
  name: 'SSEFrequencyMonitor',
  data() {
    return {
      isMonitoring: false,
      realTimeStats: {
        sseFrequency: 0,
        averageInterval: 0,
        renderDelay: 0,
        totalSSEEvents: 0,
        totalRenderEvents: 0
      },
      chartData: [], // 存储频率历史数据
      currentRecommendations: [],
      updateTimer: null
    }
  },
  mounted() {
    // 默认开始监控
    this.startMonitoring()
  },
  beforeDestroy() {
    this.stopMonitoring()
  },
  methods: {
    toggleMonitoring(enabled) {
      if (enabled) {
        this.startMonitoring()
      } else {
        this.stopMonitoring()
      }
    },
    
    startMonitoring() {
      this.isMonitoring = true
      sseAnalyzer.startAnalysis()
      
      // 每秒更新一次数据
      this.updateTimer = setInterval(() => {
        this.updateRealTimeStats()
      }, 1000)
    },
    
    stopMonitoring() {
      this.isMonitoring = false
      if (this.updateTimer) {
        clearInterval(this.updateTimer)
        this.updateTimer = null
      }
    },
    
    updateRealTimeStats() {
      if (!this.isMonitoring) return
      
      // 获取实时统计
      this.realTimeStats = sseAnalyzer.getRealTimeStats()
      
      // 更新图表数据
      this.chartData.push(this.realTimeStats.sseFrequency)
      if (this.chartData.length > 60) { // 保留最近60秒的数据
        this.chartData.shift()
      }
      
      // 生成实时建议
      this.generateRealTimeRecommendations()
    },
    
    generateRealTimeRecommendations() {
      const { sseFrequency, renderDelay } = this.realTimeStats
      const recommendations = []
      
      if (sseFrequency > 20) {
        recommendations.push({
          type: 'warning',
          title: 'SSE 频率过高',
          message: `当前 ${sseFrequency}Hz 超过推荐阈值`,
          solution: '使用 requestAnimationFrame 模式'
        })
      }
      
      if (renderDelay > 100) {
        recommendations.push({
          type: 'error',
          title: '渲染延迟过高',
          message: `当前延迟 ${renderDelay}ms 影响用户体验`,
          solution: '切换到原生DOM渲染器'
        })
      }
      
      if (sseFrequency < 2 && renderDelay < 50) {
        recommendations.push({
          type: 'success',
          title: '性能良好',
          message: '当前频率和延迟都在理想范围内',
          solution: '保持当前配置'
        })
      }
      
      this.currentRecommendations = recommendations
    },
    
    getFrequencyClass(frequency) {
      if (frequency > 20) return 'high-frequency'
      if (frequency > 5) return 'medium-frequency'
      return 'low-frequency'
    },
    
    getDelayClass(delay) {
      if (delay > 100) return 'high-delay'
      if (delay > 50) return 'medium-delay'
      return 'low-delay'
    },
    
    getRecommendedMode(frequency) {
      if (frequency > 20) return 'RAF模式'
      if (frequency > 5) return '定时器模式'
      return '立即模式'
    },
    
    getChartPoints() {
      if (this.chartData.length < 2) return ''
      
      const maxFreq = Math.max(30, Math.max(...this.chartData)) // 最小刻度30Hz
      const points = this.chartData.map((freq, index) => {
        const x = (index / (this.chartData.length - 1)) * 400
        const y = 100 - (freq / maxFreq) * 100
        return `${x},${y}`
      })
      
      return points.join(' ')
    },
    
    clearData() {
      this.chartData = []
      this.currentRecommendations = []
      sseAnalyzer.clear()
      this.realTimeStats = {
        sseFrequency: 0,
        averageInterval: 0,
        renderDelay: 0,
        totalSSEEvents: 0,
        totalRenderEvents: 0
      }
    }
  }
}
</script>

<style scoped>
.sse-frequency-monitor {
  background: white;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e4e7ed;
}

.monitor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e4e7ed;
}

.monitor-header h4 {
  margin: 0;
  color: #333;
}

.monitor-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12px;
  margin-bottom: 20px;
}

.metric-card {
  background: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
  text-align: center;
  border: 1px solid #e9ecef;
}

.metric-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.metric-value {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.high-frequency {
  color: #f56c6c;
}

.medium-frequency {
  color: #e6a23c;
}

.low-frequency {
  color: #67c23a;
}

.high-delay {
  color: #f56c6c;
}

.medium-delay {
  color: #e6a23c;
}

.low-delay {
  color: #67c23a;
}

.recommended-mode {
  color: #409eff;
  font-size: 14px;
}

.frequency-chart {
  margin-bottom: 20px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
  color: #666;
}

.chart-range {
  font-size: 12px;
  color: #999;
}

.chart-container {
  background: #fafafa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 8px;
}

.recommendations h5 {
  margin: 0 0 12px 0;
  color: #333;
}

.recommendation-item {
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 8px;
  border-left: 4px solid;
}

.recommendation-item.warning {
  background: #fdf6ec;
  border-left-color: #e6a23c;
}

.recommendation-item.error {
  background: #fef0f0;
  border-left-color: #f56c6c;
}

.recommendation-item.success {
  background: #f0f9ff;
  border-left-color: #67c23a;
}

.rec-title {
  font-weight: bold;
  margin-bottom: 4px;
  color: #333;
}

.rec-message {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.rec-solution {
  font-size: 12px;
  color: #409eff;
  font-style: italic;
}
</style>
