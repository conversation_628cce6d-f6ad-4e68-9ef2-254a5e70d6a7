import request from '@/utils/request'

/**
 * 测试接口 - 按照你提供的格式
 */
export function test() {
  return request({
    url: '/test/test1/test2',
    method: 'get'
  })
}

/**
 * 上传文件
 * @param {FormData} data - 文件数据
 */
export function uploadFile(data) {
  return request({
    url: '/upload',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 上传头像
 * @param {FormData} data - 头像文件
 */
export function uploadAvatar(data) {
  return request({
    url: '/upload/avatar',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 获取系统配置
 */
export function getSystemConfig() {
  return request({
    url: '/system/config',
    method: 'get'
  })
}

/**
 * 获取应用版本信息
 */
export function getAppVersion() {
  return request({
    url: '/system/version',
    method: 'get'
  })
}

/**
 * 发送反馈
 * @param {Object} data - 反馈数据
 * @param {string} data.type - 反馈类型
 * @param {string} data.content - 反馈内容
 * @param {string} data.contact - 联系方式（可选）
 */
export function sendFeedback(data) {
  return request({
    url: '/feedback',
    method: 'post',
    data
  })
}

/**
 * 获取公告列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.limit - 每页数量
 */
export function getAnnouncements(params) {
  return request({
    url: '/announcements',
    method: 'get',
    params
  })
}

/**
 * 健康检查
 */
export function healthCheck() {
  return request({
    url: '/health',
    method: 'get',
    showLoading: false // 不显示加载状态
  })
}

/**
 * 获取验证码
 * @param {Object} params - 参数
 * @param {string} params.email - 邮箱地址
 * @param {string} params.type - 验证码类型 (register, reset, etc.)
 */
export function getCaptcha(params) {
  return request({
    url: '/captcha',
    method: 'get',
    params
  })
}
