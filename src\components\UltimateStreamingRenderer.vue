<template>
  <div class="ultimate-streaming-renderer">
    <div v-html="renderedContent" class="markdown-content"></div>
  </div>
</template>

<script>
// 导入 MarkdownIt
import MarkdownIt from 'markdown-it'

/**
 * 终极流式 Markdown 渲染器
 * 采用最简单直接的方法，确保流式输出时能正确解析
 */
export default {
  name: 'UltimateStreamingRenderer',
  props: {
    content: {
      type: String,
      default: ''
    },
    enableHtml: {
      type: Boolean,
      default: true
    },
    enableBreaks: {
      type: Boolean,
      default: true
    },
    enableLinkify: {
      type: Boolean,
      default: true
    },
    enableTypographer: {
      type: Boolean,
      default: true
    },
    isStreaming: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      md: null,
      debugMode: true, // 开启调试模式
      lastContent: '', // 缓存上次的内容
      renderCount: 0, // 渲染计数器
      forceUpdateKey: 0 // 强制更新键
    }
  },
  computed: {
    renderedContent() {
      // 强制响应式更新
      this.forceUpdateKey

      // 调试信息
      if (this.debugMode) {
        this.renderCount++
        console.log('🔄 UltimateStreamingRenderer: computed 触发', {
          renderCount: this.renderCount,
          contentLength: this.content?.length || 0,
          lastContentLength: this.lastContent?.length || 0,
          contentChanged: this.content !== this.lastContent,
          isStreaming: this.isStreaming,
          hasMarkdown: !!this.md,
          timestamp: new Date().toLocaleTimeString()
        })
      }

      // 如果没有 Markdown 实例，尝试创建
      if (!this.md) {
        this.initMarkdown()
      }

      // 如果还是没有，返回错误信息
      if (!this.md) {
        console.error('❌ UltimateStreamingRenderer: 无法创建 Markdown 实例')
        return '<p style="color: red; font-weight: bold;">Markdown 渲染器初始化失败</p>'
      }

      // 如果没有内容，返回空
      if (!this.content) {
        this.lastContent = ''
        return ''
      }

      try {
        // 直接渲染，每次都重新渲染
        const result = this.md.render(this.content)

        // 更新缓存
        this.lastContent = this.content

        // 调试信息
        if (this.debugMode && this.isStreaming) {
          console.log(`✅ UltimateStreamingRenderer: 渲染成功 ${this.content.length} 字符`)
          if (this.content.length < 100) {
            console.log('📝 内容:', this.content)
          }
        }

        return result

      } catch (error) {
        console.error('❌ UltimateStreamingRenderer: 渲染失败', error)
        return `<p style="color: red;">渲染错误: ${error.message}</p>`
      }
    }
  },
  watch: {
    // 监听内容变化，强制更新
    content: {
      handler(newContent, oldContent) {
        if (newContent !== oldContent) {
          this.forceUpdateKey++

          // 如果是流式状态，立即强制重新渲染
          if (this.isStreaming) {
            this.$nextTick(() => {
              this.$forceUpdate()
            })
          }
        }
      },
      immediate: true
    },

    // 监听流式状态变化
    isStreaming: {
      handler(newVal, oldVal) {
        if (newVal !== oldVal) {
          console.log('🔄 UltimateStreamingRenderer: 流式状态变化', {
            from: oldVal,
            to: newVal,
            contentLength: this.content?.length || 0
          })
          this.forceUpdateKey++
          this.$forceUpdate()
        }
      }
    }
  },
  created() {
    this.initMarkdown()

    // 监听全局强制更新事件
    if (this.$bus) {
      this.$bus.$on('force-markdown-update', this.handleForceUpdate)
    }
  },

  beforeDestroy() {
    // 清理事件监听
    if (this.$bus) {
      this.$bus.$off('force-markdown-update', this.handleForceUpdate)
    }
  },
  methods: {
    // 处理强制更新事件
    handleForceUpdate(eventData) {
      // 检查是否是当前组件相关的更新
      if (eventData && this.isStreaming) {
        console.log('🔄 UltimateStreamingRenderer: 收到强制更新事件', eventData)
        this.forceUpdateKey++
        this.$forceUpdate()
      }
    },

    initMarkdown() {
      try {
        // 优先使用导入的 MarkdownIt
        if (MarkdownIt) {
          this.md = new MarkdownIt({
            html: this.enableHtml,
            xhtmlOut: false,
            breaks: this.enableBreaks,
            linkify: this.enableLinkify,
            typographer: this.enableTypographer
          })
          console.log('✅ UltimateStreamingRenderer: 使用导入的 MarkdownIt 初始化成功')
          return
        }

        // 备用方案: 全局 markdownit 函数
        if (typeof window !== 'undefined' && window.markdownit) {
          this.md = window.markdownit({
            html: this.enableHtml,
            xhtmlOut: false,
            breaks: this.enableBreaks,
            linkify: this.enableLinkify,
            typographer: this.enableTypographer
          })
          console.log('✅ UltimateStreamingRenderer: 使用全局 markdownit 初始化成功')
          return
        }

        throw new Error('无法找到 MarkdownIt')

      } catch (error) {
        console.error('❌ UltimateStreamingRenderer: 初始化失败', error)
        this.md = null
      }
    }
  }
}
</script>

<style scoped>
/* 终极流式渲染器样式 */
.ultimate-streaming-renderer {
  line-height: 1.6;
  color: var(--text-primary, #333);
  word-wrap: break-word;
  font-size: 14px;
}

.markdown-content {
  min-height: 1em; /* 确保有最小高度 */
}

/* 标题样式 */
.ultimate-streaming-renderer >>> h1,
.ultimate-streaming-renderer >>> h2,
.ultimate-streaming-renderer >>> h3,
.ultimate-streaming-renderer >>> h4,
.ultimate-streaming-renderer >>> h5,
.ultimate-streaming-renderer >>> h6 {
  color: #333;
  margin: 16px 0 12px 0;
  font-weight: 600;
  line-height: 1.4;
}

.ultimate-streaming-renderer >>> h1 {
  font-size: 1.8em;
  border-bottom: 2px solid #eee;
  padding-bottom: 8px;
}

.ultimate-streaming-renderer >>> h2 {
  font-size: 1.5em;
  border-bottom: 1px solid #eee;
  padding-bottom: 6px;
}

.ultimate-streaming-renderer >>> h3 {
  font-size: 1.3em;
  color: #409eff;
}

.ultimate-streaming-renderer >>> h4 {
  font-size: 1.1em;
  color: #606266;
}

/* 段落样式 */
.ultimate-streaming-renderer >>> p {
  margin: 12px 0;
  line-height: 1.7;
}

/* 列表样式 */
.ultimate-streaming-renderer >>> ul,
.ultimate-streaming-renderer >>> ol {
  margin: 12px 0;
  padding-left: 20px;
}

.ultimate-streaming-renderer >>> li {
  margin: 6px 0;
  line-height: 1.6;
}

/* 文本格式 */
.ultimate-streaming-renderer >>> strong {
  font-weight: 700;
  color: #409eff;
}

.ultimate-streaming-renderer >>> em {
  font-style: italic;
  color: #666;
}

.ultimate-streaming-renderer >>> del {
  text-decoration: line-through;
  color: #999;
}

/* 代码样式 */
.ultimate-streaming-renderer >>> code {
  background: rgba(64, 158, 255, 0.1);
  color: #409eff;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Courier New', Monaco, monospace;
  font-size: 0.9em;
}

.ultimate-streaming-renderer >>> pre {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
  margin: 16px 0;
  overflow-x: auto;
  font-family: 'Courier New', Monaco, monospace;
  font-size: 0.9em;
  line-height: 1.4;
}

.ultimate-streaming-renderer >>> pre code {
  background: none;
  color: #333;
  padding: 0;
  border-radius: 0;
}

/* 引用样式 */
.ultimate-streaming-renderer >>> blockquote {
  margin: 16px 0;
  padding: 12px 16px;
  border-left: 4px solid #409eff;
  background: rgba(64, 158, 255, 0.05);
  border-radius: 0 8px 8px 0;
  font-style: italic;
  color: #666;
}

.ultimate-streaming-renderer >>> blockquote p {
  margin: 0;
}

/* 表格样式 */
.ultimate-streaming-renderer >>> table {
  width: 100%;
  border-collapse: collapse;
  margin: 16px 0;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
}

.ultimate-streaming-renderer >>> th,
.ultimate-streaming-renderer >>> td {
  padding: 12px 16px;
  border-bottom: 1px solid #e9ecef;
  text-align: left;
}

.ultimate-streaming-renderer >>> th {
  background: rgba(64, 158, 255, 0.1);
  font-weight: 600;
  color: #409eff;
}

.ultimate-streaming-renderer >>> tr:last-child td {
  border-bottom: none;
}

/* 链接样式 */
.ultimate-streaming-renderer >>> a {
  color: #409eff;
  text-decoration: none;
}

.ultimate-streaming-renderer >>> a:hover {
  text-decoration: underline;
}

/* 分隔线样式 */
.ultimate-streaming-renderer >>> hr {
  border: none;
  border-top: 2px solid #eee;
  margin: 20px 0;
}

/* 图片样式 */
.ultimate-streaming-renderer >>> img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  margin: 8px 0;
}
</style>
